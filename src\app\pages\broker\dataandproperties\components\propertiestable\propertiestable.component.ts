import { ChangeDetectorRef, Component, Input, SimpleChanges, OnChanges } from '@angular/core';
import { Modal } from 'bootstrap';
import { BaseGridComponent } from 'src/app/pages/shared/base-grid/base-grid.component';
import { UnitService } from '../../../services/unit.service';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import Swal from 'sweetalert2';
import { Router } from '@angular/router';
import { PropertyService } from '../../../services/property.service';
import { TranslationService } from 'src/app/modules/i18n';

@Component({
  selector: 'app-propertiestable',
  templateUrl: './propertiestable.component.html',
  styleUrl: './propertiestable.component.scss',
})
export class PropertiestableComponent extends BaseGridComponent implements OnChanges {

  //session
  brokerId: number;

  editedIndex: number | null = null;
  newRow: any = null;
  originalRow: any;

  @Input() appliedFilters: any;
  selectedImage: string | null = null;
  selectedLocation: SafeResourceUrl | null = null;

  // Dynamic column visibility
  visibleColumns: string[] = [];

  constructor(
    protected cd: ChangeDetectorRef,
    protected unitService: UnitService,
    private sanitizer: DomSanitizer,
    private router: Router,
    private propertyService: PropertyService,
    public translationService: TranslationService
  ) {
    super(cd);
    const userJson = localStorage.getItem('currentUser');
    let user = userJson ? JSON.parse(userJson) : null;
    this.brokerId = user?.brokerId;
    this.setService(unitService);
    this.orderBy = 'id';
    this.orderDir = 'desc';
    this.page.filters = { brokerId: this.brokerId };
  }

  typeOptions: string[] = [
    'apartments', 'duplexes', 'studios', 'penthouses', 'roofs',
    'villas', 'i_villa', 'twin_houses', 'town_houses', 'administrative_units',
    'medical_clinics', 'pharmacies', 'commercial_stores', 'warehouses',
    'factory_lands', 'warehouses_land', 'standalone_villas', 'commercial_administrative_buildings',
    'commercial_administrative_lands', 'residential_buildings', 'residential_lands',
    'chalets', 'hotels', 'factories', 'basements', 'full_buildings', 'commercial_units',
    'shops', 'mixed_housings', 'cooperatives', 'youth_units', 'ganat_misr', 'dar_misr',
    'sakan_misr', 'industrial_lands', 'cabin', 'vacation_villa', 'residential_villa_lands',
    'residential_buildings_lands', 'adminstrative_lands', 'commercial_lands',
    'medical_lands', 'mixed_lands'
  ]

  viewOptions: string[] = ['water_view', 'gardens_and_landscape', 'street', 'entertainment_area', 'garden', 'main_street','square', 'side_street', 'rear_view',];
  finishingTypeOptions: string[] = ['on_brick', 'semi_finished', 'full_finished', 'company_finished', 'super_lux', 'ultra_super_lux', 'standard'];
  unitDescriptionOptions: string[] = ['single_front', 'corner', 'double_front', 'triple_corner', 'quad_corner'];
  unitDesignOptions: string[] = ['custom_design', 'one_apartment_per_floor', 'two_apartments_per_floor', 'more_than_two_apartments_per_floor'];
  unitFacingOptions: string[] = ['right_of_facade', 'left_of_facade', 'side_view', 'rear_view'];
  fitOutConditionOptions: string[] = ['unfitted', 'fully_fitted'];
  buildingDeadlineOptions: string[] = ['grace_period_allowed', 'no_grace_period'];
  furnishingStatusOptions: string[] = ['unfurnished','furnished_with_air_conditioners','furnished_without_air_conditioners'];
  activityOptions: string[] = [ 'administrative_only','commercial_only', 'medical_only', 'administrative_and_commercial', 'administrative_commercial_and_medical'];
  rentRecurrenceOptions: string[] = ['daily', 'monthly', 'quarterly', 'semi_annually', 'annually'];

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.appliedFilters && !changes.appliedFilters.firstChange) {
      this.page.filters = { brokerId: this.brokerId, ...this.appliedFilters };
      this.updateVisibleColumns();
      this.reloadTable(this.page);
    }
  }

  ngOnInit(): void {
    super.ngOnInit();
    this.updateVisibleColumns();
  }

  legalStatusOptions: string[] = [
    'licensed',
    'reconciled',
    'reconciliation_required'
  ];

  financialStatusOptions: string[] = [
    'paid_in_full',
    'partially_paid_with_remaining_installments',
  ];

  paymentSystemOptions: string[] = [
    'cash',
    'installment',
    'all_of_the_above_are_suitable'
  ];

  addNewRow() {
  this.newRow = {
    ownerName: '',
    ownerPhone: '',
    // initialize other fields as needed
  };
  this.rows.unshift(this.newRow);
  this.editedIndex = 0;
}

  editRow(index: number) {
    this.editedIndex = index;
    this.originalRow = JSON.parse(JSON.stringify(this.rows[index]));
  }

  saveRow(index: number) {
    const updatedProperty = this.rows[index];
    this.propertyService.updateProperty(updatedProperty.id, updatedProperty).subscribe({
      next:async (response:any) => {
        this.cd.detectChanges();
        await Swal.fire('Success', 'unit updated successfully', 'success');
      },
      error: (error:any) => {
        let errorMessage = 'Failed to update email';
        if (error.error && error.error.message) {
          errorMessage = error.error.message;
        }
        Swal.fire('Error', errorMessage, 'error');
      },
    });

    // Clear edit mode
    this.editedIndex = null;
    this.originalRow = null;
  }

  cancelEdit() {
    if (this.editedIndex !== null && this.originalRow) {
        this.rows[this.editedIndex] = JSON.parse(JSON.stringify(this.originalRow));
      }
      this.editedIndex = null;
      this.originalRow = null;
  }

  makeAdd(id:number){
    this.unitService.makeAdd(id).subscribe({
      next: (response:any) => {
        this.cd.detectChanges();
        Swal.fire('Units updated successfully!', '', 'success');
        document.location.reload();
      },
      error: (err:any) => {
        Swal.fire('Error making units available.', '', 'error');
        this.loading = false;
      },
    });
  }

  makeSold(id:number){
    this.unitService.makeSold(id).subscribe({
      next: (response:any) => {
        this.cd.detectChanges();
        Swal.fire('Units updated successfully!', '', 'success');
        document.location.reload();
      },
      error: (err:any) => {
        Swal.fire('Error making units available.', '', 'error');
        this.loading = false;
      },
    });
  }

  makeArchive(id:number){
    this.unitService.makeArchive(id).subscribe({
      next: (response:any) => {
        this.cd.detectChanges();
        Swal.fire('Units archived successfully!', '', 'success');
        document.location.reload();
      },
      error: (err:any) => {
        Swal.fire('Error making units available.', '', 'error');
        this.loading = false;
      },
    });
  }

  makeRequest(id:number){
    this.unitService.makeRequest(id).subscribe({
      next: async  (response:any) => {
        this.cd.detectChanges();
        await Swal.fire('request created successfully!', '', 'success');
        document.location.reload();
      },
      error: (err:any) => {
        Swal.fire('Error making request.', '', 'error');
        this.loading = false;
      },
    });
  }

  makeReply(unitId:number){
    this.router.navigate(['/requests/received'], {
    queryParams: {unitId: unitId}
    });
  }

  updateVisibleColumns(): void {
    if (this.appliedFilters?.compoundType && this.appliedFilters?.unitType) {
      this.visibleColumns = this.getFieldsToShow();
    } else {
      // Show all columns by default
      this.visibleColumns = [
        'ownerName',
        'ownerPhone',
        'type',
        'city',
        'area',
        'detailedAddress',
        'location',
        'unitPlan',
        'status',
        'otherAccessories',
        'actions',
      ];
    }
  }

  // Get fields to show based on compound type and property type
  getFieldsToShow(): string[] {
    const compoundType = this.appliedFilters?.compoundType;
    const type = this.appliedFilters?.unitType;

    // Base columns that are always shown
    const baseColumns = [
      'ownerName',
      'ownerPhone',
      'type',
      'city',
      'area',
      'subArea',
      'detailedAddress',
      'location',
    ];
    const actionColumns = ['unitPlan', 'status', 'actions'];

    let specificColumns: string[] = [];

    if (
      compoundType === 'outside_compound' &&
      (type === 'apartments' ||
        type === 'duplexes' ||
        type === 'studios' ||
        type === 'penthouses' ||
        type === 'roofs' ||
        type === 'basement')
    ) {
      specificColumns = [
        'buildingNumber',
        'unitNumber',
        'floor',
        'unitArea',
        'numberOfRooms',
        'numberOfBathrooms',
        'unitFacing',
        'view',
        'finishingType',
        'deliveryStatus',
        'legalStatus',
        'otherAccessories',
        'paymentSystem',
        'pricePerMeterInCash',
        'pricePerMeterInInstallment',
        'totalPrice',
      ];
    } else if (
      compoundType === 'outside_compound' &&
      (type == 'villas' || type == 'full_buildings')
    ) {
      specificColumns = [
        'buildingNumber',
        'numberOfFloors',
        'buildingArea',
        'groundArea',
        'unitDescription',
        'unitDesign',
        'unitFacing',
        'view',
        'finishingType',
        'legalStatus',
        'otherAccessories',
        'paymentSystem',
        'pricePerMeterInCash',
        'pricePerMeterInInstallment',
        'totalPrice',
      ];
    } else if (
      compoundType === 'outside_compound' &&
      (type === 'pharmacies' ||
        type === 'medical_clinics' ||
        type === 'administrative_units' ||
        type === 'commercial_stores')
    ) {
      specificColumns = [
        'mallName',
        'buildingNumber',
        'unitNumber',
        'floor',
        'unitArea',
        'view',
        'finishingType',
        'fitOutCondition',
        'deliveryStatus',
        'activity',
        'financialStatus',
        'otherAccessories',
        'paymentSystem',
        'pricePerMeterInCash',
        'pricePerMeterInInstallment',
        'totalPrice',
      ];
    } else if (
      compoundType === 'outside_compound' &&
      (type === 'warehouses' || type === 'factories')
    ) {
      specificColumns = [
        'buildingNumber',
        'numberOfFloors',
        'groundArea',
        'buildingArea',
        'activity',
        'finishingType',
        'unitDescription',
        'legalStatus',
        'otherAccessories',
        'paymentSystem',
        'pricePerMeterInCash',
        'pricePerMeterInInstallment',
        'totalPrice',
      ];
    } else if (
      compoundType === 'outside_compound' &&
      (type === 'residential_villa_lands' ||
        type === 'residential_lands' ||
        type === 'administrative_lands' ||
        type === 'commercial_administrative_lands' ||
        type === 'commercial_lands' ||
        type === 'medical_lands' ||
        type === 'mixed_lands' ||
        type === 'warehouses_land' ||
        type === 'industrial_lands')
    ) {
      specificColumns = [
        'unitNumber',
        'groundArea',
        'fitOutCondition',
        'unitDescription',
        'buildingDeadline',
        'view',
        'legalStatus',
        'deliveryStatus',
        'financialStatus',
        'otherAccessories',
        'paymentSystem',
        'pricePerMeterInCash',
        'pricePerMeterInInstallment',
        'totalPrice',
      ];
    } else if (
      compoundType === 'inside_compound' &&
      (type === 'apartments' ||
        type === 'duplexes' ||
        type === 'studios' ||
        type === 'penthouses' ||
        type === 'i_villa')
    ) {
      specificColumns = [
        'compoundName',
        'buildingNumber',
        'unitNumber',
        'floor',
        'unitArea',
        'numberOfRooms',
        'numberOfBathrooms',
        'view',
        'finishingType',
        'deliveryStatus',
        'financialStatus',
        'otherAccessories',
        'requestedOver',
        'paymentSystem',
        'pricePerMeterInCash',
        'pricePerMeterInInstallment',
        'totalPrice',
      ];
    } else if (
      compoundType === 'inside_compound' &&
      (type === 'standalone_villas' ||
        type === 'twin_houses' ||
        type === 'town_houses')
    ) {
      specificColumns = [
        'compoundName',
        'buildingNumber',
        'numberOfFloors',
        'buildingArea',
        'groundArea',
        'numberOfRooms',
        'numberOfBathrooms',
        'view',
        'finishingType',
        'deliveryStatus',
        'financialStatus',
        'otherAccessories',
        'requestedOver',
        'paymentSystem',
        'pricePerMeterInCash',
        'pricePerMeterInInstallment',
        'totalPrice',
      ];
    } else if (
      compoundType === 'inside_compound' &&
      (type === 'pharmacies' ||
        type === 'medical_clinics' ||
        type === 'administrative_units' ||
        type === 'commercial_stores')
    ) {
      specificColumns = [
        'compoundName',
        'mallName',
        'buildingNumber',
        'unitNumber',
        'floor',
        'unitArea',
        'view',
        'finishingType',
        'deliveryStatus',
        'fitOutCondition',
        'financialStatus',
        'otherAccessories',
        'requestedOver',
        'paymentSystem',
        'pricePerMeterInCash',
        'pricePerMeterInInstallment',
        'totalPrice',
      ];
    } else if (
      compoundType === 'village' &&
      (type === 'apartments' ||
        type === 'duplexes' ||
        type === 'studios' ||
        type === 'penthouses' ||
        type === 'basement' ||
        type === 'roofs' ||
        type === 'i_villa' ||
        type === 'villas' ||
        type === 'standalone_villas' ||
        type === 'full_buildings' ||
        type === 'twin_houses' ||
        type === 'town_houses' ||
        type === 'chalets')
    ) {
      specificColumns = [
        'buildingNumber',
        'unitNumber',
        'unitArea',
        'floor',
        'view',
        'numberOfRooms',
        'numberOfBathrooms',
        'finishingType',
        'furnishingStatus',
        'otherAccessories',
        'rentRecurrence',
        'dailyRent',
        'monthlyRent',
        'annualRent',
      ];
    } else if (
      compoundType === 'village' &&
      (type === 'warehouses' ||
        type === 'factories' ||
        type === 'administrative_units' ||
        type === 'medical_clinics' ||
        type === 'commercial_stores' ||
        type === 'pharmacies')
    ) {
      specificColumns = [
        'buildingNumber',
        'unitNumber',
        'unitArea',
        'floor',
        'view',
        'numberOfRooms',
        'numberOfBathrooms',
        'finishingType',
        'furnishingStatus',
        'otherAccessories',
        'rentRecurrence',
        'activity',
        'dailyRent',
        'monthlyRent',
      ];
    }

    return [...baseColumns, ...specificColumns, ...actionColumns];
  }

  // Check if a specific column should be shown
  shouldShowColumn(columnName: string): boolean {
    return this.visibleColumns.includes(columnName);
  }

  // Check if there are properties with daily rent
  hasPropertiesWithDailyRent(): boolean {
    return this.rows.some((property: any) =>
      property.additionalDetails?.rentRecurrence === 'daily' && property.dailyRent
    );
  }

  // Check if there are properties with monthly/annually rent
  hasPropertiesWithMonthlyRent(): boolean {
    return this.rows.some((property: any) =>
      (property.additionalDetails?.rentRecurrence === 'monthly' ||
       property.additionalDetails?.rentRecurrence === 'annually') &&
      property.monthlyRent
    );
  }

  showImageModal(location: string) {
    if (!location || location.trim() === '') {
      Swal.fire({
        title: this.getTranslatedText('WARNING'),
        text: this.getTranslatedText('NO_LOCATION_AVAILABLE'),
        icon: 'warning',
        confirmButtonText: 'OK',
      });
      return;
    }
    if (
      location.includes('maps.google.com') ||
      location.includes('maps.app.goo.gl')
    ) {
      window.open(location, '_blank');
      return;
    }

    const mapUrl = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(
      location
    )}`;
    window.open(mapUrl, '_blank');
  }

  //****************************** */

  selectedUnitPlanImage: string | null = null;

  showUnitPlanModal(imgPath: string) {
    this.selectedUnitPlanImage = imgPath;

    const modalElement = document.getElementById('viewUnitPlanModal');
    if (modalElement) {
      const modal = new Modal(modalElement);
      modal.show();
    }
  }

  viewProperty(unitService: any) {
    this.router.navigate(['/developer/projects/models/units/details'], {
      queryParams: { unitId: unitService.id },
    });
  }
  //************************************** */

  //  sortData(column: string) {
  //    if (this.orderBy === column) {
  //     this.orderDir = this.orderDir === 'asc' ? 'desc' : 'asc';
  //   } else {
  //     this.orderBy = column;
  //     this.orderDir = 'asc';
  //   }

  //    this.page.orderBy = this.orderBy;
  //   this.page.orderDir = this.orderDir;
  //   this.page.pageNumber = 0;
  //   this.reloadTable(this.page);
  // }

  //  getSortArrow(column: string): string {
  //   if (this.orderBy !== column) {
  //     return '';
  //   }
  //   return this.orderDir === 'asc' ? '↑' : '↓';
  // }

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        // Table Headers
        'OWNER_NAME': 'اسم المالك',
        'OWNER_PHONE': 'هاتف المالك',
        'UNIT_TYPE': 'نوع الوحدة',
        'CITY': 'المدينة',
        'AREA': 'المنطقة',
        'SUB_AREA': 'المنطقة الفرعية',
        'DETAILED_ADDRESS': 'العنوان التفصيلي',
        'LOCATION_ON_MAP': 'الموقع على الخريطة',
        'COMPOUND_NAME': 'اسم الكمبوند',
        'MALL_NAME': 'اسم المول',
        'BUILDING_NUMBER': 'رقم المبنى',
        'UNIT_NUMBER': 'رقم الوحدة',
        'FLOOR': 'الطابق',
        'UNIT_AREA': 'مساحة الوحدة',
        'GROUND_AREA': 'مساحة الأرض',
        'BUILDING_AREA': 'مساحة المبنى',
        'ROOMS': 'الغرف',
        'BATHROOMS': 'الحمامات',
        'NUMBER_OF_FLOORS': 'عدد الطوابق',
        'VIEW': 'الإطلالة',
        'FINISHING_TYPE': 'نوع التشطيب',
        'UNIT_DESCRIPTION': 'وصف الوحدة',
        'UNIT_DESIGN': 'تصميم الوحدة',
        'UNIT_FACING': 'اتجاه الوحدة',
        'DELIVERY_STATUS': 'حالة التسليم',
        'FIT_OUT_CONDITION': 'حالة التجهيز',
        'BUILDING_DEADLINE': 'موعد تسليم المبنى',
        'REQUESTED_OVER': 'مطلوب أكثر من',
        'FURNISHING_STATUS': 'حالة الأثاث',
        'LEGAL_STATUS': 'الحالة القانونية',
        'FINANCIAL_STATUS': 'الحالة المالية',
        'ACTIVITY': 'النشاط',
        'PAYMENT_SYSTEM': 'نظام الدفع',
        'TOTAL_PRICE': 'السعر الإجمالي',
        'RENT_RECURRENCE': 'تكرار الإيجار',
        'DAILY_RENT': 'الإيجار اليومي',
        'MONTHLY_RENT': 'الإيجار الشهري',
        'OTHER_ACCESSORIES': 'الملحقات الأخرى',
        'UNIT_PLAN': 'مخطط الوحدة',
        'STATUS': 'الحالة',
        'ACTIONS': 'الإجراءات',

        // Actions and Buttons
        'VIEW_ON_MAP': 'عرض على الخريطة',
        'VIEW_PLAN': 'عرض المخطط',
        'VIEW_UNIT_DETAILS': 'عرض تفاصيل الوحدة',
        'UNKNOWN': 'غير معروف',
        'CASH': 'نقداً',
        'INSTALLMENT': 'تقسيط',

        // Status Values
        'AVAILABLE': 'متاح',
        'SOLD': 'مباع',
        'RESERVED': 'محجوز',
        'PENDING': 'معلق',
        'NEW': 'جديد',

        // Alerts
        'WARNING': 'تحذير',
        'NO_LOCATION_AVAILABLE': 'لا يوجد موقع متاح'
      },
      'en': {
        // Table Headers
        'OWNER_NAME': 'Owner Name',
        'OWNER_PHONE': 'Owner Phone',
        'UNIT_TYPE': 'Unit Type',
        'CITY': 'City',
        'AREA': 'Area',
        'SUB_AREA': 'Sub Area',
        'DETAILED_ADDRESS': 'Detailed Address',
        'LOCATION_ON_MAP': 'Location on map',
        'COMPOUND_NAME': 'Compound Name',
        'MALL_NAME': 'Mall Name',
        'BUILDING_NUMBER': 'Building Number',
        'UNIT_NUMBER': 'Unit Number',
        'FLOOR': 'Floor',
        'UNIT_AREA': 'Unit Area',
        'GROUND_AREA': 'Ground Area',
        'BUILDING_AREA': 'Building Area',
        'ROOMS': 'Rooms',
        'BATHROOMS': 'Bathrooms',
        'NUMBER_OF_FLOORS': 'number of Floors',
        'VIEW': 'View',
        'FINISHING_TYPE': 'Finishing Type',
        'UNIT_DESCRIPTION': 'Unit Description',
        'UNIT_DESIGN': 'Unit Design',
        'UNIT_FACING': 'Unit Facing',
        'DELIVERY_STATUS': 'Delivery Status',
        'FIT_OUT_CONDITION': 'Fit Out Condition',
        'BUILDING_DEADLINE': 'Building Deadline',
        'REQUESTED_OVER': 'Requested Over',
        'FURNISHING_STATUS': 'Furnishing Status',
        'LEGAL_STATUS': 'Legal Status',
        'FINANCIAL_STATUS': 'Financial Status',
        'ACTIVITY': 'Activity',
        'PAYMENT_SYSTEM': 'Payment System',
        'TOTAL_PRICE': 'Total Price',
        'RENT_RECURRENCE': 'Rent Recurrence',
        'DAILY_RENT': 'Daily Rent',
        'MONTHLY_RENT': 'Monthly Rent',
        'OTHER_ACCESSORIES': 'Other Accessories',
        'UNIT_PLAN': 'Unit Plan',
        'STATUS': 'Status',
        'ACTIONS': 'Actions',

        // Actions and Buttons
        'VIEW_ON_MAP': 'View on map',
        'VIEW_PLAN': 'View Plan',
        'VIEW_UNIT_DETAILS': 'View unit Details',
        'UNKNOWN': 'Unknown',
        'CASH': 'Cash',
        'INSTALLMENT': 'Installment',

        // Status Values
        'AVAILABLE': 'available',
        'SOLD': 'sold',
        'RESERVED': 'reserved',
        'PENDING': 'pending',
        'NEW': 'new',

        // Alerts
        'WARNING': 'Warning',
        'NO_LOCATION_AVAILABLE': 'No location available'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }
}

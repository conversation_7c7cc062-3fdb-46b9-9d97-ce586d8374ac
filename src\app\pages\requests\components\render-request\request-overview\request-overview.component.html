<!-- Simple Request Overview -->
<div class="card border-0 shadow-sm mb-5" [class.rtl-layout]="translationService.getCurrentLanguage() === 'ar'">
  <div class="card-body p-4">
    <!-- Basic Information -->
    <div class="mb-5">
      <h6 class="text-primary mb-3 fw-bold bg-gray-100 rounded p-3"
        [class.rtl-section-header]="translationService.getCurrentLanguage() === 'ar'">
        <i class="ki-duotone ki-information-5 fs-6 text-primary"
          [class.me-2]="translationService.getCurrentLanguage() !== 'ar'"
          [class.ms-2]="translationService.getCurrentLanguage() === 'ar'">
          <span class="path1"></span>
          <span class="path2"></span>
          <span class="path3"></span>
        </i>
        {{ getTranslatedText('BASIC_INFORMATION') }}
      </h6>

      <div class="rounded p-3" [class.rtl-content]="translationService.getCurrentLanguage() === 'ar'">
        <div class="mb-2" [class.rtl-field]="translationService.getCurrentLanguage() === 'ar'">
          <span class="text-gray-600 fs-6 fw-normal"
            [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('OPERATION_TYPE')
            }}:</span>
          <span class="text-gray-800 fs-5 fw-semibold" [class.ms-2]="translationService.getCurrentLanguage() !== 'ar'"
            [class.me-2]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-value]="translationService.getCurrentLanguage() === 'ar'">{{ request?.type }}</span>
        </div>
        <div class="mb-2" [class.rtl-field]="translationService.getCurrentLanguage() === 'ar'">
          <span class="text-gray-600 fs-6 fw-normal"
            [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('SPECIALIZATION')
            }}:</span>
          <span class="text-gray-800 fs-5 fw-semibold" [class.ms-2]="translationService.getCurrentLanguage() !== 'ar'"
            [class.me-2]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-value]="translationService.getCurrentLanguage() === 'ar'">{{ request?.specializationScope
            }}</span>
        </div>
        <div class="mb-2" [class.rtl-field]="translationService.getCurrentLanguage() === 'ar'">
          <span class="text-gray-600 fs-6 fw-normal"
            [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('UNIT_TYPE')
            }}:</span>
          <span class="text-gray-800 fs-5 fw-semibold" [class.ms-2]="translationService.getCurrentLanguage() !== 'ar'"
            [class.me-2]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-value]="translationService.getCurrentLanguage() === 'ar'">{{ request?.unit }}</span>
        </div>
      </div>
    </div>

    <!-- Location Information -->
    <div class="mb-5" *ngIf="request?.locations && request?.locations.length > 0">
      <h6 class="text-primary mb-3 fw-bold bg-gray-100 rounded p-3"
        [class.rtl-section-header]="translationService.getCurrentLanguage() === 'ar'">
        <i class="ki-duotone ki-geolocation fs-6 text-primary"
          [class.me-2]="translationService.getCurrentLanguage() !== 'ar'"
          [class.ms-2]="translationService.getCurrentLanguage() === 'ar'">
          <span class="path1"></span>
          <span class="path2"></span>
        </i>
        {{ getTranslatedText('LOCATION_DETAILS') }}
      </h6>

      <div class="rounded p-3 mb-3" [class.rtl-content]="translationService.getCurrentLanguage() === 'ar'">
        <div class="row g-3">
          <div *ngFor="let cityItem of request?.locations">
            <!-- City -->
            <div class="mb-2" [class.rtl-field]="translationService.getCurrentLanguage() === 'ar'">
              <span class="text-gray-600 fs-6 fw-normal"
                [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('CITY')
                }}:</span>
              <span class="text-gray-800 fs-5 fw-semibold"
                [class.ms-2]="translationService.getCurrentLanguage() !== 'ar'"
                [class.me-2]="translationService.getCurrentLanguage() === 'ar'"
                [class.rtl-value]="translationService.getCurrentLanguage() === 'ar'">{{ cityItem.city.name_en }} | {{
                cityItem.city.name_ar }}</span>
            </div>

            <div *ngFor="let areaItem of cityItem.areas">
              <!-- Area -->
              <div class="mb-2" [class.rtl-field]="translationService.getCurrentLanguage() === 'ar'">
                <span class="text-gray-600 fs-6 fw-normal"
                  [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('AREA')
                  }}:</span>
                <span class="text-gray-800 fs-5 fw-semibold"
                  [class.ms-2]="translationService.getCurrentLanguage() !== 'ar'"
                  [class.me-2]="translationService.getCurrentLanguage() === 'ar'"
                  [class.rtl-value]="translationService.getCurrentLanguage() === 'ar'">{{ areaItem.area.name_en }} | {{
                  areaItem.area.name_ar }}</span>
              </div>

              <!-- Subarea (only if exists) -->
              <div *ngFor="let subarea of areaItem.sub_areas" class="mb-2"
                [class.rtl-field]="translationService.getCurrentLanguage() === 'ar'">
                <span class="text-gray-600 fs-6 fw-normal"
                  [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('SUBAREA')
                  }}:</span>
                <span class="text-gray-800 fs-5 fw-semibold"
                  [class.ms-2]="translationService.getCurrentLanguage() !== 'ar'"
                  [class.me-2]="translationService.getCurrentLanguage() === 'ar'"
                  [class.rtl-value]="translationService.getCurrentLanguage() === 'ar'">{{ subarea.name_en }} | {{
                  subarea.name_ar }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Additional Address Info -->
    <div class="mb-5"
      *ngIf="request?.detailedAddress || request?.attributes?.detailedAddress || request?.attributes?.addressLink">
      <h6 class="text-primary mb-3 fw-bold bg-gray-100 rounded p-3"
        [class.rtl-section-header]="translationService.getCurrentLanguage() === 'ar'">
        <i class="ki-duotone ki-map fs-6 text-primary" [class.me-2]="translationService.getCurrentLanguage() !== 'ar'"
          [class.ms-2]="translationService.getCurrentLanguage() === 'ar'">
          <span class="path1"></span>
          <span class="path2"></span>
          <span class="path3"></span>
        </i>
        {{ getTranslatedText('ADDRESS_INFORMATION') }}
      </h6>

      <div class="rounded p-3" [class.rtl-content]="translationService.getCurrentLanguage() === 'ar'">
        <div *ngIf="request?.compoundName || request?.attributes?.compoundName" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Compound Name:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.compoundName ||
            request?.attributes?.compoundName }}</span>
        </div>

        <div *ngIf="request?.detailedAddress || request?.attributes?.detailedAddress" class="mb-2"
          [class.rtl-field]="translationService.getCurrentLanguage() === 'ar'">
          <span class="text-gray-600 fs-6 fw-normal"
            [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">{{
            getTranslatedText('DETAILED_ADDRESS') }}:</span>
          <span class="text-gray-800 fs-5 fw-semibold" [class.ms-2]="translationService.getCurrentLanguage() !== 'ar'"
            [class.me-2]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-value]="translationService.getCurrentLanguage() === 'ar'">{{ request?.detailedAddress ||
            request?.attributes?.detailedAddress }}</span>
        </div>

        <div *ngIf="request?.attributes?.addressLink" class="mb-2"
          [class.rtl-field]="translationService.getCurrentLanguage() === 'ar'">
          <span class="text-gray-600 fs-6 fw-normal"
            [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('ADDRESS_LINK')
            }}:</span>
          <a [href]="request.attributes.addressLink" target="_blank" rel="noopener noreferrer"
            class="text-primary fs-5 fw-semibold text-hover-dark"
            [class.ms-2]="translationService.getCurrentLanguage() !== 'ar'"
            [class.me-2]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-value]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('VIEW_ON_MAP')
            }}</a>
        </div>
      </div>
    </div>

    <!-- Property Details -->
    <div class="mb-5"
      *ngIf="request?.attributes?.mallName || request?.attributes?.unitNumber || request?.attributes?.unitView || request?.attributes?.floor || request?.attributes?.buildingNumber || request?.attributes?.unitArea || request?.attributes?.rooms || request?.attributes?.bathRooms || request?.attributes?.gardenArea || request?.attributes?.unitFacing || request?.attributes?.floorNumber || request?.attributes?.requiredInsuranceValue|| request?.attributes?.numberOfFloors || request?.attributes?.unitLayoutStatus || request?.attributes?.unitDesign || request?.attributes?.groundArea || request?.attributes?.buildingArea || request?.attributes?.buildingLayoutStatus || request?.attributes?.terraceArea || request?.attributes?.unitDescription">
      <h6 class="text-primary mb-3 fw-bold bg-gray-100 rounded p-3"
        [class.rtl-section-header]="translationService.getCurrentLanguage() === 'ar'">
        <i class="ki-duotone ki-home-2 fs-6 text-primary"
          [class.me-2]="translationService.getCurrentLanguage() !== 'ar'"
          [class.ms-2]="translationService.getCurrentLanguage() === 'ar'">
          <span class="path1"></span>
          <span class="path2"></span>
        </i>
        {{ getTranslatedText('PROPERTY_DETAILS') }}
      </h6>

      <div class="rounded p-3" [class.rtl-content]="translationService.getCurrentLanguage() === 'ar'">
        <div *ngIf="request?.attributes?.mallName" class="mb-2"
          [class.rtl-field]="translationService.getCurrentLanguage() === 'ar'">
          <span class="text-gray-600 fs-6 fw-normal"
            [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('MALL_NAME')
            }}:</span>
          <span class="text-gray-800 fs-5 fw-semibold" [class.ms-2]="translationService.getCurrentLanguage() !== 'ar'"
            [class.me-2]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-value]="translationService.getCurrentLanguage() === 'ar'">{{ request?.attributes?.mallName
            }}</span>
        </div>
        <div *ngIf="request?.attributes?.buildingNumber" class="mb-2"
          [class.rtl-field]="translationService.getCurrentLanguage() === 'ar'">
          <span class="text-gray-600 fs-6 fw-normal"
            [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('BUILDING_NUMBER')
            }}:</span>
          <span class="text-gray-800 fs-5 fw-semibold" [class.ms-2]="translationService.getCurrentLanguage() !== 'ar'"
            [class.me-2]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-value]="translationService.getCurrentLanguage() === 'ar'">{{ request?.attributes?.buildingNumber
            }}</span>
        </div>
        <div *ngIf="request?.attributes?.unitNumber" class="mb-2"
          [class.rtl-field]="translationService.getCurrentLanguage() === 'ar'">
          <span class="text-gray-600 fs-6 fw-normal"
            [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('UNIT_NUMBER')
            }}:</span>
          <span class="text-gray-800 fs-5 fw-semibold" [class.ms-2]="translationService.getCurrentLanguage() !== 'ar'"
            [class.me-2]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-value]="translationService.getCurrentLanguage() === 'ar'">{{ request?.attributes?.unitNumber
            }}</span>
        </div>
        <div *ngIf="request?.attributes?.floor" class="mb-2"
          [class.rtl-field]="translationService.getCurrentLanguage() === 'ar'">
          <span class="text-gray-600 fs-6 fw-normal"
            [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('FLOOR')
            }}:</span>
          <span class="text-gray-800 fs-5 fw-semibold" [class.ms-2]="translationService.getCurrentLanguage() !== 'ar'"
            [class.me-2]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-value]="translationService.getCurrentLanguage() === 'ar'">{{ request?.attributes?.floor }}</span>
        </div>
        <div *ngIf="request?.attributes?.floorNumber" class="mb-2"
          [class.rtl-field]="translationService.getCurrentLanguage() === 'ar'">
          <span class="text-gray-600 fs-6 fw-normal"
            [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('FLOOR_NUMBER')
            }}:</span>
          <span class="text-gray-800 fs-5 fw-semibold" [class.ms-2]="translationService.getCurrentLanguage() !== 'ar'"
            [class.me-2]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-value]="translationService.getCurrentLanguage() === 'ar'">{{ request?.attributes?.floorNumber
            }}</span>
        </div>
        <div *ngIf="request?.attributes?.numberOfFloors" class="mb-2"
          [class.rtl-field]="translationService.getCurrentLanguage() === 'ar'">
          <span class="text-gray-600 fs-6 fw-normal"
            [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">{{
            getTranslatedText('NUMBER_OF_FLOORS') }}:</span>
          <span class="text-gray-800 fs-5 fw-semibold" [class.ms-2]="translationService.getCurrentLanguage() !== 'ar'"
            [class.me-2]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-value]="translationService.getCurrentLanguage() === 'ar'">{{ request?.attributes?.numberOfFloors
            }}</span>
        </div>
        <div *ngIf="request?.attributes?.unitArea" class="mb-2"
          [class.rtl-field]="translationService.getCurrentLanguage() === 'ar'">
          <span class="text-gray-600 fs-6 fw-normal"
            [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('UNIT_AREA')
            }}:</span>
          <span class="text-gray-800 fs-5 fw-semibold" [class.ms-2]="translationService.getCurrentLanguage() !== 'ar'"
            [class.me-2]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-value]="translationService.getCurrentLanguage() === 'ar'">{{ request?.attributes?.unitArea }}
            م²</span>
        </div>
        <div *ngIf="request?.attributes?.groundArea" class="mb-2"
          [class.rtl-field]="translationService.getCurrentLanguage() === 'ar'">
          <span class="text-gray-600 fs-6 fw-normal"
            [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('GROUND_AREA')
            }}:</span>
          <span class="text-gray-800 fs-5 fw-semibold" [class.ms-2]="translationService.getCurrentLanguage() !== 'ar'"
            [class.me-2]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-value]="translationService.getCurrentLanguage() === 'ar'">{{ request?.attributes?.groundArea }}
            م²</span>
        </div>
        <div *ngIf="request?.attributes?.buildingArea" class="mb-2"
          [class.rtl-field]="translationService.getCurrentLanguage() === 'ar'">
          <span class="text-gray-600 fs-6 fw-normal"
            [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('BUILDING_AREA')
            }}:</span>
          <span class="text-gray-800 fs-5 fw-semibold" [class.ms-2]="translationService.getCurrentLanguage() !== 'ar'"
            [class.me-2]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-value]="translationService.getCurrentLanguage() === 'ar'">{{ request?.attributes?.buildingArea }}
            م²</span>
        </div>
        <div *ngIf="request?.attributes?.rooms" class="mb-2"
          [class.rtl-field]="translationService.getCurrentLanguage() === 'ar'">
          <span class="text-gray-600 fs-6 fw-normal"
            [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('NUMBER_OF_ROOMS')
            }}:</span>
          <span class="text-gray-800 fs-5 fw-semibold" [class.ms-2]="translationService.getCurrentLanguage() !== 'ar'"
            [class.me-2]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-value]="translationService.getCurrentLanguage() === 'ar'">{{ request?.attributes?.rooms }}</span>
        </div>
        <div *ngIf="request?.attributes?.bathRooms" class="mb-2"
          [class.rtl-field]="translationService.getCurrentLanguage() === 'ar'">
          <span class="text-gray-600 fs-6 fw-normal"
            [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">{{
            getTranslatedText('NUMBER_OF_BATHROOMS') }}:</span>
          <span class="text-gray-800 fs-5 fw-semibold" [class.ms-2]="translationService.getCurrentLanguage() !== 'ar'"
            [class.me-2]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-value]="translationService.getCurrentLanguage() === 'ar'">{{ request?.attributes?.bathRooms
            }}</span>
        </div>
        <div *ngIf="request?.attributes?.gardenArea" class="mb-2"
          [class.rtl-field]="translationService.getCurrentLanguage() === 'ar'">
          <span class="text-gray-600 fs-6 fw-normal"
            [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('GARDEN_AREA')
            }}:</span>
          <span class="text-gray-800 fs-5 fw-semibold" [class.ms-2]="translationService.getCurrentLanguage() !== 'ar'"
            [class.me-2]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-value]="translationService.getCurrentLanguage() === 'ar'">{{ request?.attributes?.gardenArea }}
            م²</span>
        </div>
        <div *ngIf="request?.attributes?.terraceArea" class="mb-2"
          [class.rtl-field]="translationService.getCurrentLanguage() === 'ar'">
          <span class="text-gray-600 fs-6 fw-normal"
            [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('TERRACE_AREA')
            }}:</span>
          <span class="text-gray-800 fs-5 fw-semibold" [class.ms-2]="translationService.getCurrentLanguage() !== 'ar'"
            [class.me-2]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-value]="translationService.getCurrentLanguage() === 'ar'">{{ request?.attributes?.terraceArea }}
            م²</span>
        </div>
        <div *ngIf="request?.attributes?.unitFacing" class="mb-2"
          [class.rtl-field]="translationService.getCurrentLanguage() === 'ar'">
          <span class="text-gray-600 fs-6 fw-normal"
            [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('UNIT_FACING')
            }}:</span>
          <span class="text-gray-800 fs-5 fw-semibold" [class.ms-2]="translationService.getCurrentLanguage() !== 'ar'"
            [class.me-2]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-value]="translationService.getCurrentLanguage() === 'ar'">{{ request?.attributes?.unitFacing
            }}</span>
        </div>
        <div *ngIf="request?.attributes?.unitView" class="mb-2"
          [class.rtl-field]="translationService.getCurrentLanguage() === 'ar'">
          <span class="text-gray-600 fs-6 fw-normal"
            [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('UNIT_VIEW')
            }}:</span>
          <span class="text-gray-800 fs-5 fw-semibold" [class.ms-2]="translationService.getCurrentLanguage() !== 'ar'"
            [class.me-2]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-value]="translationService.getCurrentLanguage() === 'ar'">{{ request?.attributes?.unitView
            }}</span>
        </div>
        <div *ngIf="request?.attributes?.unitLayoutStatus" class="mb-2"
          [class.rtl-field]="translationService.getCurrentLanguage() === 'ar'">
          <span class="text-gray-600 fs-6 fw-normal"
            [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">{{
            getTranslatedText('UNIT_LAYOUT_STATUS') }}:</span>
          <span class="text-gray-800 fs-5 fw-semibold" [class.ms-2]="translationService.getCurrentLanguage() !== 'ar'"
            [class.me-2]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-value]="translationService.getCurrentLanguage() === 'ar'">{{
            request?.attributes?.unitLayoutStatus }}</span>
        </div>
        <div *ngIf="request?.attributes?.buildingLayoutStatus" class="mb-2"
          [class.rtl-field]="translationService.getCurrentLanguage() === 'ar'">
          <span class="text-gray-600 fs-6 fw-normal"
            [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">{{
            getTranslatedText('BUILDING_LAYOUT_STATUS') }}:</span>
          <span class="text-gray-800 fs-5 fw-semibold" [class.ms-2]="translationService.getCurrentLanguage() !== 'ar'"
            [class.me-2]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-value]="translationService.getCurrentLanguage() === 'ar'">{{
            request?.attributes?.buildingLayoutStatus }}</span>
        </div>
        <div *ngIf="request?.attributes?.unitDesign" class="mb-2"
          [class.rtl-field]="translationService.getCurrentLanguage() === 'ar'">
          <span class="text-gray-600 fs-6 fw-normal"
            [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('UNIT_DESIGN')
            }}:</span>
          <span class="text-gray-800 fs-5 fw-semibold" [class.ms-2]="translationService.getCurrentLanguage() !== 'ar'"
            [class.me-2]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-value]="translationService.getCurrentLanguage() === 'ar'">{{ request?.attributes?.unitDesign
            }}</span>
        </div>
        <div *ngIf="request?.attributes?.unitDescription" class="mb-2"
          [class.rtl-field]="translationService.getCurrentLanguage() === 'ar'">
          <span class="text-gray-600 fs-6 fw-normal"
            [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">{{
            getTranslatedText('UNIT_DESCRIPTION') }}:</span>
          <span class="text-gray-800 fs-5 fw-semibold" [class.ms-2]="translationService.getCurrentLanguage() !== 'ar'"
            [class.me-2]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-value]="translationService.getCurrentLanguage() === 'ar'">{{ request?.attributes?.unitDescription
            }}</span>
        </div>
      </div>
    </div>

    <!-- Status Information -->
    <div class="mb-5"
      *ngIf="request?.attributes?.deliveryStatus || request?.attributes?.financialStatus || request?.attributes?.finishingStatus || request?.attributes?.legalStatus || request?.attributes?.deliveryDate || request?.attributes?.fitOutCondition || request?.attributes?.activity || request?.attributes?.buildingDeadline || request?.attributes?.buildingLicense|| request?.attributes?.parkingStatus|| request?.attributes?.clubStatus">
      <h6 class="text-primary mb-3 fw-bold bg-gray-100 rounded p-3"
        [class.rtl-section-header]="translationService.getCurrentLanguage() === 'ar'">
        <i class="ki-duotone ki-status fs-6 text-primary"
          [class.me-2]="translationService.getCurrentLanguage() !== 'ar'"
          [class.ms-2]="translationService.getCurrentLanguage() === 'ar'">
          <span class="path1"></span>
          <span class="path2"></span>
        </i>
        {{ getTranslatedText('STATUS_INFORMATION') }}
      </h6>

      <div class="rounded p-3" [class.rtl-content]="translationService.getCurrentLanguage() === 'ar'">
        <div *ngIf="request?.attributes?.deliveryStatus" class="mb-2"
          [class.rtl-field]="translationService.getCurrentLanguage() === 'ar'">
          <span class="text-gray-600 fs-6 fw-normal"
            [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('DELIVERY_STATUS')
            }}:</span>
          <span class="text-gray-800 fs-5 fw-semibold" [class.ms-2]="translationService.getCurrentLanguage() !== 'ar'"
            [class.me-2]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-value]="translationService.getCurrentLanguage() === 'ar'">{{ request?.attributes?.deliveryStatus
            }}</span>
        </div>
        <div *ngIf="request?.attributes?.deliveryDate" class="mb-2"
          [class.rtl-field]="translationService.getCurrentLanguage() === 'ar'">
          <span class="text-gray-600 fs-6 fw-normal"
            [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('DELIVERY_DATE')
            }}:</span>
          <span class="text-gray-800 fs-5 fw-semibold" [class.ms-2]="translationService.getCurrentLanguage() !== 'ar'"
            [class.me-2]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-value]="translationService.getCurrentLanguage() === 'ar'">{{ request?.attributes?.deliveryDate
            }}</span>
        </div>
        <div *ngIf="request?.attributes?.finishingStatus" class="mb-2"
          [class.rtl-field]="translationService.getCurrentLanguage() === 'ar'">
          <span class="text-gray-600 fs-6 fw-normal"
            [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">{{
            getTranslatedText('FINISHING_STATUS') }}:</span>
          <span class="text-gray-800 fs-5 fw-semibold" [class.ms-2]="translationService.getCurrentLanguage() !== 'ar'"
            [class.me-2]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-value]="translationService.getCurrentLanguage() === 'ar'">{{ request?.attributes?.finishingStatus
            }}</span>
        </div>
        <div *ngIf="request?.attributes?.fitOutCondition" class="mb-2"
          [class.rtl-field]="translationService.getCurrentLanguage() === 'ar'">
          <span class="text-gray-600 fs-6 fw-normal"
            [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">{{
            getTranslatedText('FIT_OUT_CONDITION') }}:</span>
          <span class="text-gray-800 fs-5 fw-semibold" [class.ms-2]="translationService.getCurrentLanguage() !== 'ar'"
            [class.me-2]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-value]="translationService.getCurrentLanguage() === 'ar'">{{ request?.attributes?.fitOutCondition
            }}</span>
        </div>
        <div *ngIf="request?.attributes?.legalStatus" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Legal Status:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.legalStatus }}</span>
        </div>
        <div *ngIf="request?.attributes?.buildingLicense" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Building License:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.buildingLicense }}</span>
        </div>
        <div *ngIf="request?.attributes?.buildingDeadline" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Building Deadline:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.buildingDeadline }}</span>
        </div>
        <div *ngIf="request?.attributes?.activity" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Activity:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.activity }}</span>
        </div>
        <div *ngIf="request?.attributes?.financialStatus" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Financial Status:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.financialStatus }}</span>
        </div>
        <div *ngIf="request?.attributes?.parkingStatus" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Parking Status:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.parkingStatus }}</span>
        </div>
        <div *ngIf="request?.attributes?.clubStatus" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Club Status:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.clubStatus }}</span>
        </div>
      </div>
    </div>

    <!-- Additional Information -->
    <div class="mb-5"
      *ngIf="request?.attributes?.otherAccessories || request?.attributes?.projectConstructor || request?.attributes?.projectManagement">
      <h6 class="text-primary mb-3 fw-bold bg-gray-100 rounded p-3">
        <i class="ki-duotone ki-element-plus fs-6 text-primary me-2">
          <span class="path1"></span>
          <span class="path2"></span>
          <span class="path3"></span>
          <span class="path4"></span>
          <span class="path5"></span>
        </i>
        Additional Information
      </h6>

      <div class="rounded p-3">
        <div *ngIf="request?.attributes?.projectConstructor" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Project Constructor:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.projectConstructor }}</span>
        </div>

        <div *ngIf="request?.attributes?.projectManagement" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">project Management:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.projectManagement }}</span>
        </div>

        <span class="text-gray-600 fs-6 fw-normal">Other Accessories:</span>
        <div class="d-inline-flex flex-wrap gap-2 ms-2">
          <span *ngFor="let item of request?.attributes?.otherAccessories"
            class="badge badge-light-primary fs-8 fw-semibold px-2 py-1">
            {{ item }}
          </span>
        </div>
      </div>
    </div>

    <!-- Notes & Financial Information -->
    <div class="mb-5"
      *ngIf="request?.attributes?.totalPriceInCash ||request?.attributes?.pricePerMeterInCash ||request?.attributes?.totalPriceInInstallment ||request?.attributes?.pricePerMeterInInstallment ||request?.attributes?.dailyRent ||request?.attributes?.monthlyRent ||request?.attributes?.notes || request?.attributes?.paymentMethod || request?.attributes?.askingPrice || request?.attributes?.requestedOver || request?.attributes?.unitPrice || request?.attributes?.unitPriceSuggestions">
      <h6 class="text-primary mb-3 fw-bold bg-gray-100 rounded p-3">
        <i class="ki-duotone ki-dollar fs-6 text-primary me-2">
          <span class="path1"></span>
          <span class="path2"></span>
          <span class="path3"></span>
        </i>
        Financial & Notes
      </h6>

      <div class="rounded p-3">
        <div *ngIf="request?.attributes?.paymentMethod" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Payment Method:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.paymentMethod }}</span>
        </div>
        <div *ngIf="request?.attributes?.unitPrice" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Unit Price:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.unitPrice }} EGP</span>
        </div>
        <div *ngIf="request?.attributes?.unitPriceSuggestions" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Unit Price Suggestion:</span>
          <span class="badge fs-6 fw-semibold ms-2" [ngClass]="{
            'badge-light-success': request?.attributes?.unitPriceSuggestions === 1,
            'badge-light-danger': request?.attributes?.unitPriceSuggestions !== 1
          }">
            {{ (request?.attributes?.unitPriceSuggestions === 1 ? 'APPROVED' : 'PENDING') }}
          </span>
        </div>
        <div *ngIf="request?.attributes?.totalPriceInCash" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">total Price InCash:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.totalPriceInCash }} EGP</span>
        </div>
        <div *ngIf="request?.attributes?.pricePerMeterInCash" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">price PerMeter InCash:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.pricePerMeterInCash }} EGP</span>
        </div>
        <div *ngIf="request?.attributes?.totalPriceInInstallment" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">total Price InInstallment:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.totalPriceInInstallment }}
            EGP</span>
        </div>
        <div *ngIf="request?.attributes?.pricePerMeterInInstallment" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">price PerMeter InInstallment:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.pricePerMeterInInstallment }}
            EGP</span>
        </div>
        <div *ngIf="request?.attributes?.dailyRent" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">daily Rent:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.dailyRent }} EGP</span>
        </div>
        <div *ngIf="request?.attributes?.monthlyRent" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">monthly Rent:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.monthlyRent }} EGP</span>
        </div>
        <div *ngIf="request?.attributes?.askingPrice" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Asking Price:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.askingPrice }} EGP</span>
        </div>
        <div *ngIf="request?.attributes?.requestedOver" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Requested Over:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.requestedOver }} EGP</span>
        </div>
        <div *ngIf="request?.attributes?.notes" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Notes:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.notes }}</span>
        </div>
      </div>
    </div>

    <!-- Media Gallery -->
    <div class="mb-5"
      *ngIf="request?.mainImage || (request?.gallery && request?.gallery?.length > 0) || request?.unitInMasterPlanImage">
      <h6 class="text-primary mb-3 fw-bold bg-gray-100 rounded p-3">
        <i class="ki-duotone ki-picture fs-6 text-primary me-2">
          <span class="path1"></span>
          <span class="path2"></span>
        </i>
        Media Gallery
      </h6>

      <div class="rounded p-3">
        <!-- Main Image -->
        <div class="mb-2 d-flex align-items-center" *ngIf="request?.mainImage">
          <span class="text-gray-600 fs-6 me-3" style="min-width: 120px;">
            <i class="ki-duotone ki-picture fs-6 text-primary me-1">
              <span class="path1"></span>
              <span class="path2"></span>
            </i>
            Main Image :
          </span>
          <img [src]="request?.mainImage" alt="Main Image" class="rounded cursor-pointer shadow-sm"
            style="height: 30px; width: 50px; object-fit: cover;"
            (click)="openImageModal(request?.mainImage, 'Main Image')" data-bs-toggle="modal"
            data-bs-target="#mediaModal">
        </div>

        <!-- Gallery Media (Images and Videos) -->
        <div class="mb-2 d-flex align-items-center" *ngIf="request?.gallery?.length > 0">
          <span class="text-gray-600 fs-6 me-3" style="min-width: 120px;">
            <i class="ki-duotone ki-gallery fs-6 text-info me-1">
              <span class="path1"></span>
              <span class="path2"></span>
            </i>
            Gallery Media :
          </span>
          <div class="d-flex gap-2">
            <ng-container *ngFor="let media of request?.gallery; let i = index">
              <!-- Image -->
              <img *ngIf="media.type === 'image'" [src]="media.url" [alt]="'Gallery Image ' + (i + 1)"
                class="rounded cursor-pointer shadow-sm" style="height: 30px; width: 50px; object-fit: cover;"
                (click)="openImageModal(media.url, 'Gallery Image ' + (i + 1))" data-bs-toggle="modal"
                data-bs-target="#mediaModal">

              <!-- Video -->
              <div *ngIf="media.type === 'video'" class="position-relative rounded cursor-pointer shadow-sm"
                style="height: 30px; width: 50px; background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iMzAiIHZpZXdCb3g9IjAgMCA1MCAzMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjUwIiBoZWlnaHQ9IjMwIiBmaWxsPSIjMzc0MTUxIi8+CjxwYXRoIGQ9Ik0yMCAxMEwyOCAxNUwyMCAyMFYxMFoiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo=') center/cover; background-color: #374151;"
                (click)="openVideoModal(media.url, 'Gallery Video ' + (i + 1))" data-bs-toggle="modal"
                data-bs-target="#mediaModal">
                <div class="position-absolute top-50 start-50 translate-middle">
                  <i class="ki-duotone ki-play-circle fs-4 text-white opacity-75">
                    <span class="path1"></span>
                    <span class="path2"></span>
                  </i>
                </div>
              </div>
            </ng-container>
          </div>
        </div>

        <!-- Unit in Master Plan Image -->

        <div class="mb-2 d-flex align-items-center" *ngIf="request?.unitInMasterPlanImage">
          <span class="text-gray-600 fs-6 me-3" style="min-width: 120px;">
            <i class="ki-duotone ki-design-frame fs-6 text-success me-1">
              <span class="path1"></span>
              <span class="path2"></span>
            </i>
            Unit Plan :
          </span>
          <img [src]="request?.unitInMasterPlanImage" alt="Unit in Master Plan" class="rounded cursor-pointer shadow-sm"
            style="height: 30px; width: 50px; object-fit: cover;"
            (click)="openImageModal(request?.unitInMasterPlanImage, 'Unit in Master Plan')" data-bs-toggle="modal"
            data-bs-target="#mediaModal">
        </div>
      </div>
    </div>

    <!-- Created At -->
    <div class="mb-4">
      <h6 class="text-primary mb-3 fw-bold bg-gray-100 rounded p-3">
        <i class="ki-duotone ki-calendar-2 fs-6 text-primary me-2">
          <span class="path1"></span>
          <span class="path2"></span>
          <span class="path3"></span>
          <span class="path4"></span>
        </i>
        Request Information
      </h6>

      <div class="rounded p-3">
        <span class="text-gray-600 fs-6 fw-normal">Created At:</span>
        <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.createdAt }}</span>
      </div>
    </div>
  </div>
</div>

<!-- Media Modal -->
<div class="modal fade" id="mediaModal" tabindex="-1" aria-labelledby="mediaModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-md modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="mediaModalLabel">{{ modalTitle }}</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body text-center">
        <!-- Image Display -->
        <img *ngIf="modalType === 'image'" [src]="modalContent" [alt]="modalTitle" class="img-fluid rounded shadow"
          style="max-height: 300px;">

        <!-- Video Display -->
        <video *ngIf="modalType === 'video'" [src]="modalContent" controls class="w-100 rounded shadow"
          style="max-height: 500px;">
          Your browser does not support the video tag.
        </video>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>

      </div>
    </div>
  </div>
</div>

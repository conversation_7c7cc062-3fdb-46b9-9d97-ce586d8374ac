import { Component, OnInit, ChangeDetectorRef, Type } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { PropertyService } from '../services/property.service';
import Swal from 'sweetalert2';
import { TranslationService } from 'src/app/modules/i18n';
import { PropertyTranslationService } from 'src/app/shared/services/property-translation.service';
@Component({
  selector: 'app-add-property',
  templateUrl: './add-property.component.html',
  styleUrl: './add-property.component.scss',
})


/*

    {  key: 'I Villa', value: 'i_villa' },
    { key: 'Residential Buildings', value: 'residential_buildings' },
    { key: 'Mixed Housings', value: ' mixed_housings' },
    { key: 'cooperatives', value: ' cooperatives' },
    { key: 'youth units', value: 'youth_units' },
    { key: 'ganat misr', value: 'ganat_misr' },
    { key: 'dar misr', value: 'dar_misr' },
    { key: 'sakan misr', value: 'sakan_misr' },
    { key: 'industrial_lands', value: 'industrial_lands' },
    { key: ' cabin', value: ' cabin' },
    { key: 'vacation villa', value: ' vacation_villa' },


{ key: 'Hotels', value: 'hotels' },


    { key: 'Commercial Administrative Buildings', value: 'commercial_administrative_buildings' },
    { key: 'Commercial Stores', value: 'commercial_stores' },
    { key: 'commercial_units', value: ' commercial_units' },
    { key: 'shops', value: 'shops' },



*/
export class AddPropertyComponent implements OnInit {
  totalSteps = 5;
  currentStep = 0;
  selectedCityId: any;
  selectedCityName: string;
  selectedAreaName: string;
  selectedSubAreaName: string;
  selectedUnitType: string;
  cities: any[] = [];
  unitTypes: { key: string; value: string }[] = [];
  areas: any[] = [];
  subAreas: any[] = [];

  // Loading states
  isSubmittingDraft = false;
  isSubmittingPublish = false;
  isLoadingCities = false;

  otherAccessoriesList = [
  { key: 'GARAGE', value: 'garage' },
  { key: 'CLUBHOUSE', value: 'clubhouse' },
  { key: 'CLUB', value: 'club' },
  { key: 'STORAGE', value: 'storage' },
  { key: 'ELEVATOR', value: 'elevator' },
  { key: 'SWIMMING POOL', value: 'swimming_pool' },
  { key: 'ALL THE ABOVE', value: 'all_the_above_are_suitable' }
];


  //get brokerId from session
  brokerId: number;

  finishingType: { key: string; value: string }[] = [
    { key: 'On Brick', value: 'on_brick' },
    { key: 'Semi Finished', value: 'semi_finished' },
    { key: 'Company Finished', value: 'company_finished' },
    { key: 'Super Lux', value: 'super_lux' },
    { key: 'Ultra Super Lux', value: 'ultra_super_lux' },
  ];

  floorTypes: { key: string; value: string }[] = [
    { key: 'Ground', value: 'ground' },
    { key: 'Last Floor', value: 'last_floor' },
    { key: 'Repeated', value: 'repeated' },
    { key: 'All Of The Above', value: 'all_the_above_are_suitable' },
  ];

  viewTypes: { key: string; value: string }[] = [
    { key: 'Water View', value: 'water_view' },
    { key: 'Gardens And Landscape', value: 'gardens_and_landscape' },
    { key: 'Street', value: 'street' },
    { key: 'Entertainment Area', value: 'entertainment_area' },
    { key: 'Garden', value: 'garden' },
    { key: 'Main Street', value: 'main_street' },
    { key: 'Square', value: 'square' },
    { key: 'Side Street', value: 'side_street' },
    { key: 'Rear View', value: 'rear_view' },
  ];

  deliveryTypes: { key: string; value: string }[] = [
    { key: 'Immediate Delivery', value: 'immediate_delivery' },
    { key: 'Under Construction', value: 'under_construction' },
  ];

  activityTypes: { key: string; value: string }[] = [
    { key: 'Administrative Only', value: 'administrative_only' },
    { key: 'Commercial Only', value: 'commercial_only' },
    { key: 'Medical Only', value: 'medical_only' },
    {
      key: 'Administrative And Commercial',
      value: 'administrative_and_commercial',
    },
    {
      key: 'Administrative Commercial And Medical',
      value: 'administrative_commercial_and_medical',
    },
  ];

  fitOutConditionTypes: { key: string; value: string }[] = [
    { key: 'Unfitted', value: 'unfitted' },
    { key: 'Fully Fitted', value: 'fully_fitted' },
    { key: 'All The Above Are Suitable', value: 'all_the_above_are_suitable' },
  ];

  furnishingStatusTypes: { key: string; value: string }[] = [
    { key: 'Unfurnished', value: 'unfurnished' },
    {
      key: 'Furnished With Air Conditioners',
      value: 'furnished_with_air_conditioners',
    },
    {
      key: 'Furnished Without Air Conditioners',
      value: 'furnished_without_air_conditioners',
    },
  ];

  groundLayoutStatusTypes: { key: string; value: string }[] = [
    { key: 'Vacant Land', value: 'vacant_land' },
    { key: 'Under Construction', value: 'under_construction' },
    { key: 'Fully Built', value: 'fully_built' },
    { key: 'All Acceptable', value: 'all_acceptable' },
  ];

  unitDesignTypes: { key: string; value: string }[] = [
    { key: 'Custom Design', value: 'custom_design' },
    { key: 'One Apartment Per Floor', value: 'one_apartment_per_floor' },
    { key: 'Two Apartments Per Floor', value: 'two_apartments_per_floor' },
    {
      key: 'More Than Two Apartments Per Floor',
      value: 'more_than_two_apartments_per_floor',
    },
    { key: 'All Acceptable', value: 'all_acceptable' },
  ];

  otherAccessoriesTypes: { key: string; value: string }[] = [
    { key: 'Garage', value: 'garage' },
    { key: 'Clubhouse', value: 'clubhouse' },
    { key: 'Club', value: 'club' },
    { key: 'Storage', value: 'storage' },
    { key: 'Elevator', value: 'elevator' },
    { key: 'Swimming Pool', value: 'swimming_pool' },
  ];

  selectedAccessories: string[] = [];

  paymentTypes: { key: string; value: string }[] = [
    { key: 'Cash', value: 'cash' },
    { key: 'Installment', value: 'installment' },
    {
      key: 'All Of The Above Are Suitable ',
      value: 'all_of_the_above_are_suitable',
    },
  ];

  legalTypes: { key: string; value: string }[] = [
    { key: 'Licensed', value: 'licensed' },
    { key: 'Reconciled', value: 'reconciled' },
    { key: 'Reconciliation Required', value: 'reconciliation_required' },
  ];

  financialStatusTypes: { key: string; value: string }[] = [
    { key: 'paid_in_full ', value: 'paid_in_full' },
    { key: 'partially_paid_with_remaining_installments ', value: 'partially_paid_with_remaining_installments' },

  ];

  buildingDeadlineTypes: { key: string; value: string }[] = [
    { key: 'Grace Period Allowed', value: 'grace_period_allowed' },
    { key: 'No Grace Period', value: 'no_grace_period' },
  ];

  // Step 0 options
  compoundOptions: { key: string; value: string }[] = [
    { key: 'Outside Compound', value: 'outside_compound' },
    { key: 'Inside Compound', value: 'inside_compound' },
    { key: 'village', value: 'village'},
  ];

  propertyTypeOptions: { key: string; value: string }[] = [
    { key: 'Sell', value: 'sell' },
    { key: 'Rent', value: 'rent_out' },
  ];

  rentRecurrenceTypes: { key: string; value: string }[] = [
    { key: 'Monthly', value: 'monthly' },
    { key: 'Daily', value: 'daily' },
    { key: 'Annually', value: 'annually' },
  ];


  // All unit types for filtering
  allUnitTypes: { key: string; value: string }[] = [];

  // Unit types for outside compound
  outsideCompoundUnitTypes: { key: string; value: string }[] = [
    // Residential
    { key: 'Apartments', value: 'apartments' },
    { key: 'Duplexes', value: 'duplexes' },
    { key: 'Studios', value: 'studios' },
    { key: 'Penthouses', value: 'penthouses' },
    { key: 'Basement', value: 'basements' },
    { key: 'Roofs', value: 'roofs' },

    // Villas
    { key: 'Villas', value: 'villas' },
    { key: 'Full Buildings', value: 'full_buildings' },


    // Commercial/Administrative
    { key: 'Administrative Units', value: 'administrative_units' },
    { key: 'Medical Clinics', value: 'medical_clinics' },
    { key: 'Commercial Stores', value: 'commercial_stores' },
    { key: 'Pharmacies', value: 'pharmacies' },


    // Industrial
    { key: 'Warehouses', value: 'warehouses' },
    { key: 'Factories', value: 'factories' },


    // Lands
    { key: 'Residential Villa Lands', value: 'residential_villa_lands' },
    { key: 'Administrative lands', value: 'administrative_lands' },
    { key: 'Residential Lands', value: 'residential_lands' },
    { key: 'Commercial Administrative Lands', value: 'commercial_administrative_lands' },
    { key: 'Medical Lands', value: 'medical_lands' },
    { key: 'Mixed Lands', value: 'mixed_lands' },
    { key: 'Warehouses Land', value: 'warehouses_land' },
    { key: 'Factory Lands', value: 'factory_lands' },
  ];

   insideCompoundUnitTypes: { key: string; value: string }[] = [
    // Residential
    { key: 'Apartments', value: 'apartments' },
    { key: 'Duplexes', value: 'duplexes' },
    { key: 'Studios', value: 'studios' },
    { key: 'Penthouses', value: 'penthouses' },
    { key: 'I Villa', value: 'i_villa' },

    //villas
    { key: 'Standalone Villas', value: 'standalone_villas' },
    { key: 'Town Houses', value: 'town_houses' },
    { key: 'Twin Houses', value: 'twin_houses' },


    // // Commercial/Administrative
    { key: 'Administrative Units', value: 'administrative_units' },
    { key: 'Medical Clinics', value: 'medical_clinics' },
    { key: 'Commercial Stores', value: 'commercial_stores' },
    { key: 'Pharmacies', value: 'pharmacies' },



   ];
   RentalUnitTypes: { key: string; value: string }[] = [
    // Residential
      { key: 'Apartments', value: 'apartments' },
      { key: 'Duplexes', value: 'duplexes' },
      { key: 'Studios', value: 'studios' },
      { key: 'Penthouses', value: 'penthouses' },
      { key: 'Basement', value: 'basements' },
      { key: 'Roofs', value: 'roofs' },

      // villas
      { key: 'I Villa', value: 'i_villa' },
      { key: 'Twin Houses', value: 'twin_houses' },
      { key: 'Town Houses', value: 'town_houses' },
      { key: 'Standalone Villas', value: 'standalone_villas' },
      { key: 'Villas', value: 'villas' },
      { key: 'Full Buildings', value: 'full_buildings' },

      // Commercial/Administrative
      { key: 'Administrative Units', value: 'administrative_units' },
      { key: 'Medical Clinics', value: 'medical_clinics' },
      { key: 'Commercial Stores', value: 'commercial_stores' },
      { key: 'Pharmacies', value: 'pharmacies' },

      //other
      { key: 'chalets', value: 'chalets' },

     // Industrial
      { key: 'Warehouses', value: 'warehouses' },
      { key: 'Factories', value: 'factories' },

   ];


  // Filtered unit types based on compound selection
  filteredUnitTypes: { key: string; value: string }[] = [];

  step0Form: FormGroup;
  step1Form: FormGroup;
  step2Form: FormGroup;
  step3Form: FormGroup;
  step4Form: FormGroup;
  step5Form: FormGroup;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private propertyService: PropertyService,
    private cdr: ChangeDetectorRef,
    public translationService: TranslationService,
    private propertyTranslationService: PropertyTranslationService
  ) {}

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        // Headers
        'ADD_PROPERTY': 'إضافة عقار',
        'ADD_UNIT': 'إضافة وحدة',
        'PROPERTY_CATEGORY': 'فئة العقار',
        'LOCATION_INFORMATION': 'معلومات الموقع',
        'UNIT_INFORMATION': 'معلومات الوحدة',
        'PAYMENT_DETAILS': 'تفاصيل الدفع',
        'MEDIA_DOCUMENTS': 'الوسائط والمستندات',
        'REVIEW_SUBMIT': 'مراجعة وإرسال',

        // Progress
        'STEP': 'الخطوة',
        'OF': 'من',
        'BACK_TO_PREVIOUS_STEP': 'العودة للخطوة السابقة',

        // Step 0
        'COMPOUND_TYPE': 'نوع الكمبوند',
        'SELECT_COMPOUND_TYPE': 'اختر نوع الكمبوند',
        'PROPERTY_TYPE': 'نوع العقار',
        'SELECT_PROPERTY_TYPE': 'اختر نوع العقار',
        'UNIT_TYPE': 'نوع الوحدة',
        'SELECT_UNIT_TYPE': 'اختر نوع الوحدة',
        'SELECT_COMPOUND_TYPE_FIRST': 'اختر نوع الكمبوند أولاً',

        // Step 1
        'OWNER_NAME': 'اسم المالك',
        'ENTER_NAME': 'أدخل الاسم',
        'OWNER_PHONE': 'هاتف المالك',
        'ENTER_PHONE_NUMBER': 'أدخل رقم الهاتف',
        'PHONE_REQUIRED': 'رقم الهاتف مطلوب.',
        'PHONE_PATTERN_ERROR': 'يرجى إدخال رقم هاتف مصري صحيح (مثال: 01XXXXXXXXX).',
        'CITY': 'المدينة',
        'SELECT_CITY': 'اختر المدينة',
        'LOADING_CITIES': 'جاري تحميل المدن...',
        'NO_CITIES_AVAILABLE': 'لا توجد مدن متاحة',
        'LOADING': 'جاري التحميل...',
        'TOTAL_CITIES': 'إجمالي - المدن',
        'AREA': 'المنطقة',
        'SELECT_AREA': 'اختر المنطقة',
        'SUB_AREA': 'المنطقة الفرعية',
        'SELECT_SUB_AREA': 'اختر المنطقة الفرعية',
        'DETAILED_ADDRESS': 'العنوان التفصيلي',
        'ENTER_DETAILED_ADDRESS': 'أدخل العنوان التفصيلي',
        'MALL_NAME': 'اسم المول',
        'ENTER_MALL_NAME': 'أدخل اسم المول (اختياري)',
        'COMPOUND_NAME': 'اسم الكمبوند',
        'ENTER_COMPOUND_NAME': 'أدخل اسم الكمبوند (اختياري)',
        'GOOGLE_MAPS_LINK': 'رابط خرائط جوجل',
        'ENTER_GOOGLE_MAPS_LINK': 'أدخل رابط خرائط جوجل',
        'GOOGLE_MAPS_REQUIRED': 'رابط خرائط جوجل مطلوب.',
        'GOOGLE_MAPS_PATTERN_ERROR': 'يرجى إدخال رابط صحيح (مثال: https://maps.google.com/...).',
        'MALL_NAME_MAX_LENGTH': 'اسم المول لا يمكن أن يتجاوز 255 حرف.',
        'COMPOUND_NAME_MAX_LENGTH': 'اسم الكمبوند لا يمكن أن يتجاوز 255 حرف.',
        'NAME_REQUIRED': 'الاسم مطلوب.',
        'ADDRESS_REQUIRED': 'العنوان التفصيلي مطلوب.',
        'CITY_REQUIRED': 'المدينة مطلوبة.',
        'AREA_REQUIRED': 'المنطقة مطلوبة.',
        'SUB_AREA_REQUIRED': 'المنطقة الفرعية مطلوبة.',

        // Step 2 - Unit Information
        'BUILDING_NUMBER': 'رقم المبنى',
        'ENTER_BUILDING_NUMBER': 'أدخل رقم المبنى',
        'UNIT_NUMBER': 'رقم الوحدة',
        'ENTER_UNIT_NUMBER': 'أدخل رقم الوحدة',
        'FLOOR': 'الطابق',
        'SELECT_FLOOR': 'اختر الطابق',
        'UNIT_AREA_SQM': 'مساحة الوحدة (متر مربع)',
        'ENTER_UNIT_AREA': 'أدخل المساحة بالمتر المربع',
        'BUILDING_AREA_SQM': 'مساحة المبنى (متر مربع)',
        'ENTER_BUILDING_AREA': 'أدخل مساحة المبنى بالمتر المربع',
        'GROUND_AREA_SQM': 'مساحة الأرض (متر مربع)',
        'ENTER_GROUND_AREA': 'أدخل مساحة الأرض بالمتر المربع',
        'NUMBER_OF_ROOMS': 'عدد الغرف',
        'ENTER_NUMBER_OF_ROOMS': 'أدخل عدد الغرف',
        'NUMBER_OF_BATHROOMS': 'عدد الحمامات',
        'ENTER_NUMBER_OF_BATHROOMS': 'أدخل عدد الحمامات',
        'NUMBER_OF_FLOORS': 'عدد الطوابق',
        'ENTER_NUMBER_OF_FLOORS': 'أدخل عدد الطوابق',
        'FACING_LOCATION': 'موقع الإطلالة',
        'SELECT_FACING_LOCATION': 'اختر موقع الشقة',
        'UNIT_DESCRIPTION': 'وصف الوحدة',
        'SELECT_UNIT_DESCRIPTION': 'اختر وصف الوحدة',
        'BUILDING_DEADLINE': 'موعد تسليم المبنى',
        'SELECT_BUILDING_DEADLINE': 'اختر موعد تسليم المبنى',
        'VIEW': 'الإطلالة',
        'SELECT_VIEW': 'اختر إطلالة الشقة',
        'FINISHING_STATUS': 'حالة التشطيب',
        'SELECT_FINISHING_STATUS': 'اختر حالة التشطيب',
        'FIT_OUT_CONDITION': 'حالة التجهيز',
        'SELECT_FIT_OUT_CONDITION': 'اختر حالة التجهيز',
        'FURNISHING_STATUS': 'حالة الأثاث',
        'SELECT_FURNISHING_STATUS': 'اختر حالة الأثاث',
        'GROUND_LAYOUT_STATUS': 'حالة تخطيط الأرض',
        'SELECT_GROUND_LAYOUT_STATUS': 'اختر حالة تخطيط الأرض',
        'UNIT_DESIGN': 'تصميم الوحدة',
        'SELECT_UNIT_DESIGN': 'اختر تصميم الوحدة',
        'ACTIVITY': 'النشاط',
        'SELECT_ACTIVITY': 'اختر النشاط',
        'DELIVERY_STATUS': 'حالة التسليم',
        'SELECT_DELIVERY_STATUS': 'اختر حالة التسليم',
        'DELIVERY_DATE': 'تاريخ التسليم',
        'SELECT_DELIVERY_DATE': 'اختر تاريخ التسليم',
        'LEGAL_STATUS': 'الحالة القانونية',
        'SELECT_LEGAL_STATUS': 'اختر الحالة القانونية',
        'FINANCIAL_STATUS': 'الحالة المالية',
        'SELECT_FINANCIAL_STATUS': 'اختر الحالة المالية',
        'OTHER_ACCESSORIES': 'الملحقات الأخرى',
        'SELECT_ADDITIONAL_AMENITIES': 'اختر الملحقات الإضافية',

        // Step 3 - Payment Details
        'REQUESTED_OVER': 'مطلوب أكثر من',
        'ENTER_REQUESTED_OVER': 'أدخل المطلوب أكثر من (اختياري)',
        'RENT_RECURRENCE': 'تكرار الإيجار',
        'SELECT_RENT_RECURRENCE': 'اختر تكرار الإيجار',
        'DAILY_RENT': 'الإيجار اليومي',
        'ENTER_DAILY_RENT': 'أدخل مبلغ الإيجار اليومي',
        'MONTHLY_RENT': 'الإيجار الشهري',
        'ENTER_MONTHLY_RENT': 'أدخل مبلغ الإيجار الشهري',
        'PAYMENT_SYSTEM': 'نظام الدفع',
        'SELECT_PAYMENT_SYSTEM': 'اختر نظام الدفع',
        'PRICE_PER_METER_CASH': 'السعر للمتر نقداً',
        'ENTER_PRICE_PER_METER_CASH': 'أدخل السعر للمتر نقداً',
        'TOTAL_PRICE_CASH': 'إجمالي السعر نقداً',
        'ENTER_TOTAL_PRICE_CASH': 'أدخل إجمالي السعر نقداً',
        'PRICE_PER_METER_INSTALLMENT': 'السعر للمتر بالتقسيط',
        'ENTER_PRICE_PER_METER_INSTALLMENT': 'أدخل السعر للمتر بالتقسيط',
        'TOTAL_PRICE_INSTALLMENT': 'إجمالي السعر بالتقسيط',
        'ENTER_TOTAL_PRICE_INSTALLMENT': 'أدخل إجمالي السعر بالتقسيط',
        'DAILY_RENT_MIN_ERROR': 'الإيجار اليومي يجب أن يكون أكبر من أو يساوي 0.',
        'MONTHLY_RENT_MIN_ERROR': 'الإيجار الشهري يجب أن يكون أكبر من أو يساوي 0.',
        'REQUESTED_OVER_MAX_LENGTH': 'المطلوب أكثر من لا يمكن أن يتجاوز 255 حرف.',

        // Step 4 - Media & Documents
        'UPLOAD_IMAGES': 'رفع الصور',
        'UPLOAD_VIDEOS': 'رفع الفيديوهات',
        'UPLOAD_DOCUMENTS': 'رفع المستندات',
        'DRAG_DROP_FILES': 'اسحب وأفلت الملفات هنا أو',
        'CLICK_TO_BROWSE': 'انقر للتصفح',
        'SUPPORTED_FORMATS': 'الصيغ المدعومة',
        'MAX_FILE_SIZE': 'الحد الأقصى لحجم الملف',
        'IMAGES_FORMATS': 'JPG, PNG, GIF',
        'VIDEOS_FORMATS': 'MP4, AVI, MOV',
        'DOCUMENTS_FORMATS': 'PDF, DOC, DOCX',
        'MAX_SIZE_10MB': '10 ميجابايت',
        'MAX_SIZE_50MB': '50 ميجابايت',
        'MAX_SIZE_5MB': '5 ميجابايت',
        'REMOVE': 'إزالة',
        'PREVIEW': 'معاينة',
        'UPLOAD_SUCCESS': 'تم الرفع بنجاح',
        'UPLOAD_ERROR': 'خطأ في الرفع',
        'FILE_TOO_LARGE': 'حجم الملف كبير جداً',
        'INVALID_FILE_TYPE': 'نوع الملف غير مدعوم',
        'NO_FILES_SELECTED': 'لم يتم اختيار ملفات',
        'UPLOADING': 'جاري الرفع...',
        'SELECT_FILES': 'اختر الملفات',

        // Step 5 - Review & Submit
        'READY_TO_SUBMIT': 'جاهز لإرسال العقار',
        'REVIEW_INFORMATION': 'يرجى مراجعة جميع المعلومات التي أدخلتها والنقر على إرسال لإضافة العقار.',
        'NOTE': 'ملاحظة:',
        'OWNER_INFO_STEP1': '• معلومات المالك يتم جمعها في الخطوة 1',
        'LEGAL_STATUS_STEP2': '• الحالة القانونية هي جزء من الخطوة 2 (معلومات الوحدة)',
        'FORMS_REORGANIZED': '• تم إعادة تنظيم جميع النماذج لتحسين تجربة المستخدم',
        'ADD_PROPERTY_DRAFT': 'إضافة عقار',
        'ADD_PROPERTY_PUBLISH': 'إضافة عقار ونشر',
        'SUBMITTING': 'جاري الإرسال...',

        // Navigation Buttons
        'CANCEL': 'إلغاء',
        'NEXT': 'التالي',
        'NEXT_LOCATION_INFORMATION': 'التالي - معلومات الموقع',
        'NEXT_UNIT_INFORMATION': 'التالي - معلومات الوحدة',
        'NEXT_PAYMENT_DETAILS': 'التالي - تفاصيل الدفع',
        'NEXT_MEDIA_DOCUMENTS': 'التالي - الوسائط والمستندات',
        'NEXT_REVIEW_SUBMIT': 'التالي - مراجعة وإرسال',

        // Progress Text
        'STEP_PROGRESS': 'الخطوة {current} من {total}',

        // Dropdown Options
        'OUTSIDE_COMPOUND': 'خارج الكمبوند',
        'INSIDE_COMPOUND': 'داخل الكمبوند',
        'SELL': 'بيع',
        'RENT': 'إيجار',
        'RENT_IN': 'إيجار داخلي',

        // Rent Recurrence Options
        'Daily': 'يومي',
        'Monthly': 'شهري',
        'Annually': 'سنوي',
        'daily': 'يومي',
        'monthly': 'شهري',
        'annually': 'سنوي',

        // Payment System Options
        'Cash': 'نقداً',
        'Installment': 'تقسيط',
        'Both': 'كلاهما',
        'cash': 'نقداً',
        'installment': 'تقسيط',
        'both': 'كلاهما',

        // Step 2 Additional Options
        'Right Of Facade': 'يمين الواجهة',
        'Left Of Facade': 'يسار الواجهة',
        'Side View': 'إطلالة جانبية',
        'Rear View': 'إطلالة خلفية',
        'Single Front': 'واجهة واحدة',
        'Corner': 'ركن',
        'Double Front': 'واجهتان',
        'Triple Corner': 'ثلاثة أركان',
        'Quad Corner': 'أربعة أركان',
        'All Acceptable': 'جميع الخيارات مقبولة',
        'All The Above Are Suitable': 'جميع ما سبق مناسب',



        // Additional Dropdown Options
        'GARAGE': 'جراج',
        'CLUBHOUSE': 'نادي اجتماعي',
        'CLUB': 'نادي',
        'STORAGE': 'مخزن',
        'ELEVATOR': 'مصعد',
        'SWIMMING POOL': 'حمام سباحة',
        'ALL THE ABOVE': 'جميع ما سبق',
        'Garage': 'جراج',
        'Clubhouse': 'نادي اجتماعي',
        'Club': 'نادي',
        'Storage': 'مخزن',
        'Elevator': 'مصعد',
        'Swimming Pool': 'حمام سباحة',
        'On Brick': 'على الطوب',
        'Semi Finished': 'نصف تشطيب',
        'Company Finished': 'تشطيب شركة',
        'Super Lux': 'سوبر لوكس',
        'Ultra Super Lux': 'ألترا سوبر لوكس',
        'Ground': 'أرضي',
        'Last Floor': 'الطابق الأخير',
        'Repeated': 'متكرر',
        'All Of The Above': 'جميع ما سبق',
        'Water View': 'إطلالة مائية',
        'Gardens And Landscape': 'حدائق ومناظر طبيعية',
        'Street': 'شارع',
        'Entertainment Area': 'منطقة ترفيهية',
        'Garden': 'حديقة',
        'Main Street': 'شارع رئيسي',
        'Square': 'ميدان',
        'Side Street': 'شارع جانبي',
        'Immediate Delivery': 'تسليم فوري',
        'Administrative Only': 'إداري فقط',
        'Commercial Only': 'تجاري فقط',
        'Medical Only': 'طبي فقط',
        'Administrative And Commercial': 'إداري وتجاري',
        'Administrative Commercial And Medical': 'إداري وتجاري وطبي',
        'Unfitted': 'غير مجهز',
        'Fully Fitted': 'مجهز بالكامل',
        'Unfurnished': 'غير مفروش',
        'Furnished With Air Conditioners': 'مفروش مع تكييف',
        'Furnished Without Air Conditioners': 'مفروش بدون تكييف',
        'Vacant Land': 'أرض فضاء',
        'Fully Built': 'مبني بالكامل',
        'Custom Design': 'تصميم مخصص',
        'One Apartment Per Floor': 'شقة واحدة في الطابق',
        'Two Apartments Per Floor': 'شقتان في الطابق',
        'More Than Two Apartments Per Floor': 'أكثر من شقتين في الطابق',
        'Licensed': 'مرخص',
        'Reconciled': 'مصالح عليه',
        'Reconciliation Required': 'يتطلب مصالحة',
        'paid_in_full': 'مدفوع بالكامل',
        'partially_paid_with_remaining_installments': 'مدفوع جزئياً مع أقساط متبقية',
        'Grace Period Allowed': 'فترة سماح مسموحة',
        'No Grace Period': 'بدون فترة سماح',

        // Additional View Types (with exact spacing)
        'Garden  ': 'حديقة',
        ' Main Street': 'شارع رئيسي',

        // SweetAlert Messages
        'SUCCESS': 'نجح!',
        'ERROR': 'خطأ!',
        'WARNING': 'تحذير!',
        'CONFIRM': 'تأكيد',
        'CANCEL_ACTION': 'إلغاء',
        'OK': 'موافق',
        'PROPERTY_ADDED_SUCCESS': 'تم إضافة العقار بنجاح!',
        'PROPERTY_PUBLISHED_SUCCESS': 'تم إضافة ونشر العقار بنجاح!',
        'FORM_VALIDATION_ERROR': 'يرجى التحقق من صحة البيانات المدخلة.',
        'NETWORK_ERROR': 'خطأ في الشبكة. يرجى المحاولة مرة أخرى.',
        'UNEXPECTED_ERROR': 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.',
        'CONFIRM_CANCEL': 'هل أنت متأكد من إلغاء إضافة العقار؟',
        'UNSAVED_CHANGES': 'ستفقد جميع التغييرات غير المحفوظة.',
        'YES_CANCEL': 'نعم، إلغاء',
        'NO_STAY': 'لا، البقاء'
      },
      'en': {
        // Headers
        'ADD_PROPERTY': 'Add Property',
        'ADD_UNIT': 'Add Unit',
        'PROPERTY_CATEGORY': 'Property Category',
        'LOCATION_INFORMATION': 'Location Information',
        'UNIT_INFORMATION': 'Unit Information',
        'PAYMENT_DETAILS': 'Payment Details',
        'MEDIA_DOCUMENTS': 'Media & Documents',
        'REVIEW_SUBMIT': 'Review & Submit',

        // Progress
        'STEP': 'Step',
        'OF': 'of',
        'BACK_TO_PREVIOUS_STEP': 'Back to previous step',

        // Step 0
        'COMPOUND_TYPE': 'Compound Type',
        'SELECT_COMPOUND_TYPE': 'Select Compound Type',
        'PROPERTY_TYPE': 'Property Type',
        'SELECT_PROPERTY_TYPE': 'Select property type',
        'UNIT_TYPE': 'Unit Type',
        'SELECT_UNIT_TYPE': 'Select Unit Type',
        'SELECT_COMPOUND_TYPE_FIRST': 'Please select compound type first',

        // Step 1
        'OWNER_NAME': 'Owner Name',
        'ENTER_NAME': 'Enter name',
        'OWNER_PHONE': 'Owner Phone',
        'ENTER_PHONE_NUMBER': 'Enter phone number',
        'PHONE_REQUIRED': 'Phone number is required.',
        'PHONE_PATTERN_ERROR': 'Please enter a valid Egyptian phone number (e.g., 01XXXXXXXXX).',
        'CITY': 'City',
        'SELECT_CITY': 'Select City',
        'LOADING_CITIES': 'Loading cities...',
        'NO_CITIES_AVAILABLE': 'No cities available',
        'LOADING': 'Loading...',
        'TOTAL_CITIES': 'Total - Cities',
        'AREA': 'Area',
        'SELECT_AREA': 'Select Area',
        'SUB_AREA': 'Sub Area',
        'SELECT_SUB_AREA': 'Select sub area',
        'DETAILED_ADDRESS': 'Detailed Address',
        'ENTER_DETAILED_ADDRESS': 'Enter detailed address',
        'MALL_NAME': 'Mall Name',
        'ENTER_MALL_NAME': 'Enter mall name (optional)',
        'COMPOUND_NAME': 'Compound Name',
        'ENTER_COMPOUND_NAME': 'Enter compound name (optional)',
        'GOOGLE_MAPS_LINK': 'Google Maps Link',
        'ENTER_GOOGLE_MAPS_LINK': 'Enter Google Maps link',
        'GOOGLE_MAPS_REQUIRED': 'Google Maps link is required.',
        'GOOGLE_MAPS_PATTERN_ERROR': 'Please enter a valid URL (e.g., https://maps.google.com/...).',
        'MALL_NAME_MAX_LENGTH': 'Mall name cannot exceed 255 characters.',
        'COMPOUND_NAME_MAX_LENGTH': 'Compound name cannot exceed 255 characters.',
        'NAME_REQUIRED': 'Name is required.',
        'ADDRESS_REQUIRED': 'Detailed address is required.',
        'CITY_REQUIRED': 'City is required.',
        'AREA_REQUIRED': 'Area is required.',
        'SUB_AREA_REQUIRED': 'Sub area is required.',

        // Step 2 - Unit Information
        'BUILDING_NUMBER': 'Building Number',
        'ENTER_BUILDING_NUMBER': 'Enter building number',
        'UNIT_NUMBER': 'Unit Number',
        'ENTER_UNIT_NUMBER': 'Enter unit number',
        'FLOOR': 'Floor',
        'SELECT_FLOOR': 'Select floor',
        'UNIT_AREA_SQM': 'Unit Area (sqm)',
        'ENTER_UNIT_AREA': 'Enter area in square meters',
        'BUILDING_AREA_SQM': 'Building Area (sqm)',
        'ENTER_BUILDING_AREA': 'Enter building area in square meters',
        'GROUND_AREA_SQM': 'Ground Area (sqm)',
        'ENTER_GROUND_AREA': 'Enter ground area in square meters',
        'NUMBER_OF_ROOMS': 'Number of Rooms',
        'ENTER_NUMBER_OF_ROOMS': 'Enter number of rooms',
        'NUMBER_OF_BATHROOMS': 'Number of Bathrooms',
        'ENTER_NUMBER_OF_BATHROOMS': 'Enter number of bathrooms',
        'NUMBER_OF_FLOORS': 'Number of Floors',
        'ENTER_NUMBER_OF_FLOORS': 'Enter number of floors',
        'FACING_LOCATION': 'Facing Location',
        'SELECT_FACING_LOCATION': 'Select apartment location',
        'UNIT_DESCRIPTION': 'Unit Description',
        'SELECT_UNIT_DESCRIPTION': 'Select unit description',
        'BUILDING_DEADLINE': 'Building Deadline',
        'SELECT_BUILDING_DEADLINE': 'Select building deadline',
        'VIEW': 'View',
        'SELECT_VIEW': 'Select apartment view',
        'FINISHING_STATUS': 'Finishing Status',
        'SELECT_FINISHING_STATUS': 'Select finishing status',
        'FIT_OUT_CONDITION': 'Fit Out Condition',
        'SELECT_FIT_OUT_CONDITION': 'Select fit out condition',
        'FURNISHING_STATUS': 'Furnishing Status',
        'SELECT_FURNISHING_STATUS': 'Select furnishing status',
        'GROUND_LAYOUT_STATUS': 'Ground Layout Status',
        'SELECT_GROUND_LAYOUT_STATUS': 'Select ground layout status',
        'UNIT_DESIGN': 'Unit Design',
        'SELECT_UNIT_DESIGN': 'Select unit design',
        'ACTIVITY': 'Activity',
        'SELECT_ACTIVITY': 'Select activity',
        'DELIVERY_STATUS': 'Delivery Status',
        'SELECT_DELIVERY_STATUS': 'Select delivery status',
        'DELIVERY_DATE': 'Delivery Date',
        'SELECT_DELIVERY_DATE': 'Select delivery date',
        'LEGAL_STATUS': 'Legal Status',
        'SELECT_LEGAL_STATUS': 'Select legal status',
        'FINANCIAL_STATUS': 'Financial Status',
        'SELECT_FINANCIAL_STATUS': 'Select financial status',
        'OTHER_ACCESSORIES': 'Other Accessories',
        'SELECT_ADDITIONAL_AMENITIES': 'Select additional amenities',

        // Step 3 - Payment Details
        'REQUESTED_OVER': 'Requested Over',
        'ENTER_REQUESTED_OVER': 'Enter requested over (optional)',
        'RENT_RECURRENCE': 'Rent Recurrence',
        'SELECT_RENT_RECURRENCE': 'Select rent recurrence',
        'DAILY_RENT': 'Daily Rent',
        'ENTER_DAILY_RENT': 'Enter daily rent amount',
        'MONTHLY_RENT': 'Monthly Rent',
        'ENTER_MONTHLY_RENT': 'Enter monthly rent amount',
        'PAYMENT_SYSTEM': 'Payment System',
        'SELECT_PAYMENT_SYSTEM': 'Select payment system',
        'PRICE_PER_METER_CASH': 'Price Per Meter In Cash',
        'ENTER_PRICE_PER_METER_CASH': 'Enter price per meter in cash',
        'TOTAL_PRICE_CASH': 'Total Price In Cash',
        'ENTER_TOTAL_PRICE_CASH': 'Enter total price in cash',
        'PRICE_PER_METER_INSTALLMENT': 'Price Per Meter In Installment',
        'ENTER_PRICE_PER_METER_INSTALLMENT': 'Enter price per meter in installment',
        'TOTAL_PRICE_INSTALLMENT': 'Total Price In Installment',
        'ENTER_TOTAL_PRICE_INSTALLMENT': 'Enter total price in installment',
        'DAILY_RENT_MIN_ERROR': 'Daily rent must be greater than or equal to 0.',
        'MONTHLY_RENT_MIN_ERROR': 'Monthly rent must be greater than or equal to 0.',
        'REQUESTED_OVER_MAX_LENGTH': 'Requested over cannot exceed 255 characters.',

        // Step 4 - Media & Documents
        'UPLOAD_IMAGES': 'Upload Images',
        'UPLOAD_VIDEOS': 'Upload Videos',
        'UPLOAD_DOCUMENTS': 'Upload Documents',
        'DRAG_DROP_FILES': 'Drag and drop files here or',
        'CLICK_TO_BROWSE': 'click to browse',
        'SUPPORTED_FORMATS': 'Supported formats',
        'MAX_FILE_SIZE': 'Max file size',
        'IMAGES_FORMATS': 'JPG, PNG, GIF',
        'VIDEOS_FORMATS': 'MP4, AVI, MOV',
        'DOCUMENTS_FORMATS': 'PDF, DOC, DOCX',
        'MAX_SIZE_10MB': '10MB',
        'MAX_SIZE_50MB': '50MB',
        'MAX_SIZE_5MB': '5MB',
        'REMOVE': 'Remove',
        'PREVIEW': 'Preview',
        'UPLOAD_SUCCESS': 'Upload successful',
        'UPLOAD_ERROR': 'Upload error',
        'FILE_TOO_LARGE': 'File too large',
        'INVALID_FILE_TYPE': 'Invalid file type',
        'NO_FILES_SELECTED': 'No files selected',
        'UPLOADING': 'Uploading...',
        'SELECT_FILES': 'Select files',

        // Step 5 - Review & Submit
        'READY_TO_SUBMIT': 'Ready to Submit Property',
        'REVIEW_INFORMATION': 'Please review all the information you\'ve entered and click submit to add the property.',
        'NOTE': 'Note:',
        'OWNER_INFO_STEP1': '• Owner information is now collected in Step 1',
        'LEGAL_STATUS_STEP2': '• Legal Status is now part of Step 2 (Unit Information)',
        'FORMS_REORGANIZED': '• All forms have been reorganized for better user experience',
        'ADD_PROPERTY_DRAFT': 'Add Property',
        'ADD_PROPERTY_PUBLISH': 'Add Property & Publish',
        'SUBMITTING': 'Submitting...',

        // Navigation Buttons
        'CANCEL': 'Cancel',
        'NEXT': 'Next',
        'NEXT_LOCATION_INFORMATION': 'Next - Location Information',
        'NEXT_UNIT_INFORMATION': 'Next - Unit Information',
        'NEXT_PAYMENT_DETAILS': 'Next - Payment Details',
        'NEXT_MEDIA_DOCUMENTS': 'Next - Media & Documents',
        'NEXT_REVIEW_SUBMIT': 'Next - Review & Submit',

        // Progress Text
        'STEP_PROGRESS': 'Step {current} of {total}',

        // Dropdown Options
        'OUTSIDE_COMPOUND': 'Outside Compound',
        'INSIDE_COMPOUND': 'Inside Compound',
        'SELL': 'Sell',
        'RENT': 'Rent',
        'RENT_IN': 'Rent In',

        // Rent Recurrence Options
        'Daily': 'Daily',
        'Monthly': 'Monthly',
        'Annually': 'Annually',
        'daily': 'Daily',
        'monthly': 'Monthly',
        'annually': 'Annually',

        // Payment System Options
        'Cash': 'Cash',
        'Installment': 'Installment',
        'Both': 'Both',
        'cash': 'Cash',
        'installment': 'Installment',
        'both': 'Both',

        // Step 2 Additional Options
        'Right Of Facade': 'Right Of Facade',
        'Left Of Facade': 'Left Of Facade',
        'Side View': 'Side View',
        'Rear View': 'Rear View',
        'Single Front': 'Single Front',
        'Corner': 'Corner',
        'Double Front': 'Double Front',
        'Triple Corner': 'Triple Corner',
        'Quad Corner': 'Quad Corner',
        'All Acceptable': 'All Acceptable',
        'All The Above Are Suitable': 'All The Above Are Suitable',

        // Additional English Options (matching Arabic translations)
        'GARAGE': 'GARAGE',
        'CLUBHOUSE': 'CLUBHOUSE',
        'CLUB': 'CLUB',
        'STORAGE': 'STORAGE',
        'ELEVATOR': 'ELEVATOR',
        'SWIMMING POOL': 'SWIMMING POOL',
        'ALL THE ABOVE': 'ALL THE ABOVE',
        'Garage': 'Garage',
        'Clubhouse': 'Clubhouse',
        'Club': 'Club',
        'Storage': 'Storage',
        'Elevator': 'Elevator',
        'Swimming Pool': 'Swimming Pool',
        'On Brick': 'On Brick',
        'Semi Finished': 'Semi Finished',
        'Company Finished': 'Company Finished',
        'Super Lux': 'Super Lux',
        'Ultra Super Lux': 'Ultra Super Lux',
        'Ground': 'Ground',
        'Last Floor': 'Last Floor',
        'Repeated': 'Repeated',
        'All Of The Above': 'All Of The Above',
        'Water View': 'Water View',
        'Gardens And Landscape': 'Gardens And Landscape',
        'Street': 'Street',
        'Entertainment Area': 'Entertainment Area',
        'Garden': 'Garden',
        'Main Street': 'Main Street',
        'Square': 'Square',
        'Side Street': 'Side Street',
        'Immediate Delivery': 'Immediate Delivery',
        'Administrative Only': 'Administrative Only',
        'Commercial Only': 'Commercial Only',
        'Medical Only': 'Medical Only',
        'Administrative And Commercial': 'Administrative And Commercial',
        'Administrative Commercial And Medical': 'Administrative Commercial And Medical',
        'Unfitted': 'Unfitted',
        'Fully Fitted': 'Fully Fitted',
        'Unfurnished': 'Unfurnished',
        'Furnished With Air Conditioners': 'Furnished With Air Conditioners',
        'Furnished Without Air Conditioners': 'Furnished Without Air Conditioners',
        'Vacant Land': 'Vacant Land',
        'Fully Built': 'Fully Built',
        'Custom Design': 'Custom Design',
        'One Apartment Per Floor': 'One Apartment Per Floor',
        'Two Apartments Per Floor': 'Two Apartments Per Floor',
        'More Than Two Apartments Per Floor': 'More Than Two Apartments Per Floor',
        'Licensed': 'Licensed',
        'Reconciled': 'Reconciled',
        'Reconciliation Required': 'Reconciliation Required',
        'paid_in_full': 'Paid In Full',
        'partially_paid_with_remaining_installments': 'Partially Paid With Remaining Installments',
        'Grace Period Allowed': 'Grace Period Allowed',
        'No Grace Period': 'No Grace Period',

        // Additional View Types (with exact spacing)
        'Garden  ': 'Garden',
        ' Main Street': 'Main Street',

        // SweetAlert Messages
        'SUCCESS': 'Success!',
        'ERROR': 'Error!',
        'WARNING': 'Warning!',
        'CONFIRM': 'Confirm',
        'CANCEL_ACTION': 'Cancel',
        'OK': 'OK',
        'PROPERTY_ADDED_SUCCESS': 'Property added successfully!',
        'PROPERTY_PUBLISHED_SUCCESS': 'Property added and published successfully!',
        'FORM_VALIDATION_ERROR': 'Please check the validity of the entered data.',
        'NETWORK_ERROR': 'Network error. Please try again.',
        'UNEXPECTED_ERROR': 'An unexpected error occurred. Please try again.',
        'CONFIRM_CANCEL': 'Are you sure you want to cancel adding the property?',
        'UNSAVED_CHANGES': 'You will lose all unsaved changes.',
        'YES_CANCEL': 'Yes, Cancel',
        'NO_STAY': 'No, Stay'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }

  // Helper method to translate dropdown options
  getTranslatedOption(key: string): string {
    if (!key) return '';

    // Clean the key by trimming whitespace
    const cleanKey = key.trim();

    const optionTranslations: any = {
      'ar': {
        'Outside Compound': 'خارج الكمبوند',
        'Inside Compound': 'داخل الكمبوند',
        'Village': 'قرية',
        'Sell': 'بيع',
        'Rent': 'إيجار',
        'Rent In': 'إيجار داخلي',
        'Purchasing': 'شراء',
        'outside_compound': 'خارج الكمبوند',
        'inside_compound': 'داخل الكمبوند',
        'village': 'قرية',
        'sell': 'بيع',
        'rent': 'إيجار',
        'rent_in': 'إيجار داخلي',
        'purchasing': 'شراء',

        // View Types
        'Water View': 'إطلالة مائية',
        'Gardens And Landscape': 'حدائق ومناظر طبيعية',
        'Street': 'شارع',
        'Entertainment Area': 'منطقة ترفيهية',
        'Garden': 'حديقة',
        'Main Street': 'شارع رئيسي',
        'Square': 'ميدان',
        'Side Street': 'شارع جانبي',
        'Rear View': 'إطلالة خلفية',

        // Finishing Types
        'On Brick': 'على الطوب',
        'Semi Finished': 'نصف تشطيب',
        'Company Finished': 'تشطيب شركة',
        'Super Lux': 'سوبر لوكس',
        'Ultra Super Lux': 'ألترا سوبر لوكس',

        // Floor Types
        'Ground': 'أرضي',
        'Last Floor': 'الطابق الأخير',
        'Repeated': 'متكرر',
        'All Of The Above': 'جميع ما سبق',

        // Other common options
        'Immediate Delivery': 'تسليم فوري',
        'Under Construction': 'تحت الإنشاء',
        'Administrative Only': 'إداري فقط',
        'Commercial Only': 'تجاري فقط',
        'Medical Only': 'طبي فقط',
        'Administrative And Commercial': 'إداري وتجاري',
        'Administrative Commercial And Medical': 'إداري وتجاري وطبي',
        'Unfitted': 'غير مجهز',
        'Fully Fitted': 'مجهز بالكامل',
        'Unfurnished': 'غير مفروش',
        'Furnished With Air Conditioners': 'مفروش مع تكييف',
        'Furnished Without Air Conditioners': 'مفروش بدون تكييف',
        'Vacant Land': 'أرض فضاء',
        'Fully Built': 'مبني بالكامل',
        'Custom Design': 'تصميم مخصص',
        'One Apartment Per Floor': 'شقة واحدة في الطابق',
        'Two Apartments Per Floor': 'شقتان في الطابق',
        'More Than Two Apartments Per Floor': 'أكثر من شقتين في الطابق',
        'Licensed': 'مرخص',
        'Reconciled': 'مصالح عليه',
        'Reconciliation Required': 'يتطلب مصالحة',
        'Grace Period Allowed': 'فترة سماح مسموحة',
        'No Grace Period': 'بدون فترة سماح',
        'GARAGE': 'جراج',
        'CLUBHOUSE': 'نادي اجتماعي',
        'CLUB': 'نادي',
        'STORAGE': 'مخزن',
        'ELEVATOR': 'مصعد',
        'SWIMMING POOL': 'حمام سباحة',
        'ALL THE ABOVE': 'جميع ما سبق',
        'Garage': 'جراج',
        'Clubhouse': 'نادي اجتماعي',
        'Club': 'نادي',
        'Storage': 'مخزن',
        'Elevator': 'مصعد',
        'Swimming Pool': 'حمام سباحة',

        // Financial and Payment Types
        'Cash': 'نقداً',
        'Installment': 'تقسيط',
        'All Of The Above Are Suitable': 'جميع ما سبق مناسب',
        'paid_in_full': 'مدفوع بالكامل',
        'partially_paid_with_remaining_installments': 'مدفوع جزئياً مع أقساط متبقية',

        // Rent Recurrence
        'Monthly': 'شهري',
        'Daily': 'يومي',
        'Annually': 'سنوي',

        // Unit Facing Options
        'Right Of Facade': 'يمين الواجهة',
        'Left Of Facade': 'يسار الواجهة',
        'Side View': 'إطلالة جانبية',
        'Single Front': 'واجهة واحدة',
        'Corner': 'ركن',
        'Double Front': 'واجهتان',
        'Triple Corner': 'ثلاثة أركان',
        'Quad Corner': 'أربعة أركان',
        'All Acceptable': 'جميع الخيارات مقبولة',

        // Additional Step 2 Options
        'paid_in_full ': 'مدفوع بالكامل',
        'partially_paid_with_remaining_installments ': 'مدفوع جزئياً مع أقساط متبقية',

        // Lowercase versions
        'water view': 'إطلالة مائية',
        'gardens and landscape': 'حدائق ومناظر طبيعية',
        'street': 'شارع',
        'entertainment area': 'منطقة ترفيهية',
        'garden': 'حديقة',
        'main street': 'شارع رئيسي',
        'square': 'ميدان',
        'side street': 'شارع جانبي',
        'rear view': 'إطلالة خلفية',
        'on brick': 'على الطوب',
        'semi finished': 'نصف تشطيب',
        'company finished': 'تشطيب شركة',
        'super lux': 'سوبر لوكس',
        'ultra super lux': 'ألترا سوبر لوكس',
        'ground': 'أرضي',
        'last floor': 'الطابق الأخير',
        'repeated': 'متكرر',
        'all of the above': 'جميع ما سبق',
        'All The Above Are Suitable': 'جميع ما سبق مناسب',
        'all the above are suitable': 'جميع ما سبق مناسب'
      },
      'en': {
        'Outside Compound': 'Outside Compound',
        'Inside Compound': 'Inside Compound',
        'Village': 'Village',
        'Sell': 'Sell',
        'Rent': 'Rent',
        'Rent In': 'Rent In',
        'Purchasing': 'Purchasing',
        'outside_compound': 'Outside Compound',
        'inside_compound': 'Inside Compound',
        'village': 'Village',
        'sell': 'Sell',
        'rent': 'Rent',
        'rent_in': 'Rent In',
        'purchasing': 'Purchasing',

        // English options (same as key)
        'Water View': 'Water View',
        'Gardens And Landscape': 'Gardens And Landscape',
        'Street': 'Street',
        'Entertainment Area': 'Entertainment Area',
        'Garden': 'Garden',
        'Main Street': 'Main Street',
        'Square': 'Square',
        'Side Street': 'Side Street',
        'Rear View': 'Rear View',
        'On Brick': 'On Brick',
        'Semi Finished': 'Semi Finished',
        'Company Finished': 'Company Finished',
        'Super Lux': 'Super Lux',
        'Ultra Super Lux': 'Ultra Super Lux',
        'Ground': 'Ground',
        'Last Floor': 'Last Floor',
        'Repeated': 'Repeated',
        'All Of The Above': 'All Of The Above',
        'Immediate Delivery': 'Immediate Delivery',
        'Under Construction': 'Under Construction',
        'Administrative Only': 'Administrative Only',
        'Commercial Only': 'Commercial Only',
        'Medical Only': 'Medical Only',
        'Administrative And Commercial': 'Administrative And Commercial',
        'Administrative Commercial And Medical': 'Administrative Commercial And Medical',
        'Unfitted': 'Unfitted',
        'Fully Fitted': 'Fully Fitted',
        'Unfurnished': 'Unfurnished',
        'Furnished With Air Conditioners': 'Furnished With Air Conditioners',
        'Furnished Without Air Conditioners': 'Furnished Without Air Conditioners',
        'Vacant Land': 'Vacant Land',
        'Fully Built': 'Fully Built',
        'Custom Design': 'Custom Design',
        'One Apartment Per Floor': 'One Apartment Per Floor',
        'Two Apartments Per Floor': 'Two Apartments Per Floor',
        'More Than Two Apartments Per Floor': 'More Than Two Apartments Per Floor',
        'Licensed': 'Licensed',
        'Reconciled': 'Reconciled',
        'Reconciliation Required': 'Reconciliation Required',
        'Grace Period Allowed': 'Grace Period Allowed',
        'No Grace Period': 'No Grace Period',
        'GARAGE': 'GARAGE',
        'CLUBHOUSE': 'CLUBHOUSE',
        'CLUB': 'CLUB',
        'STORAGE': 'STORAGE',
        'ELEVATOR': 'ELEVATOR',
        'SWIMMING POOL': 'SWIMMING POOL',
        'ALL THE ABOVE': 'ALL THE ABOVE',
        'Garage': 'Garage',
        'Clubhouse': 'Clubhouse',
        'Club': 'Club',
        'Storage': 'Storage',
        'Elevator': 'Elevator',
        'Swimming Pool': 'Swimming Pool',

        // Financial and Payment Types
        'Cash': 'Cash',
        'Installment': 'Installment',
        'All Of The Above Are Suitable': 'All Of The Above Are Suitable',
        'paid_in_full': 'Paid In Full',
        'partially_paid_with_remaining_installments': 'Partially Paid With Remaining Installments',

        // Rent Recurrence
        'Monthly': 'Monthly',
        'Daily': 'Daily',
        'Annually': 'Annually',

        // Unit Facing Options
        'Right Of Facade': 'Right Of Facade',
        'Left Of Facade': 'Left Of Facade',
        'Side View': 'Side View',
        'Single Front': 'Single Front',
        'Corner': 'Corner',
        'Double Front': 'Double Front',
        'Triple Corner': 'Triple Corner',
        'Quad Corner': 'Quad Corner',
        'All Acceptable': 'All Acceptable',

        // Additional Step 2 Options
        'paid_in_full ': 'Paid In Full',
        'partially_paid_with_remaining_installments ': 'Partially Paid With Remaining Installments',

        // Lowercase versions
        'water view': 'Water View',
        'gardens and landscape': 'Gardens And Landscape',
        'street': 'Street',
        'entertainment area': 'Entertainment Area',
        'garden': 'Garden',
        'main street': 'Main Street',
        'square': 'Square',
        'side street': 'Side Street',
        'rear view': 'Rear View',
        'on brick': 'On Brick',
        'semi finished': 'Semi Finished',
        'company finished': 'Company Finished',
        'super lux': 'Super Lux',
        'ultra super lux': 'Ultra Super Lux',
        'ground': 'Ground',
        'last floor': 'Last Floor',
        'repeated': 'Repeated',
        'all of the above': 'All Of The Above',
        'All The Above Are Suitable': 'All The Above Are Suitable',
        'all the above are suitable': 'All The Above Are Suitable'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();

    // Try with original key first, then with cleaned key
    return optionTranslations[currentLang]?.[key] ||
           optionTranslations[currentLang]?.[cleanKey] ||
           cleanKey || key;
  }

  // Helper method to translate unit types using PropertyTranslationService
  getTranslatedUnitType(unitTypeKey: string): string {
    if (!unitTypeKey) return '';
    const currentLang = this.translationService.getCurrentLanguage() as 'en' | 'ar';
    return this.propertyTranslationService.translatePropertyType(unitTypeKey, currentLang, 50);
  }

  // Helper methods for city and area names
  getCityName(city: any): string {
    const currentLang = this.translationService.getCurrentLanguage();
    return currentLang === 'ar' ? (city.name_ar || city.name_en) : city.name_en;
  }

  getAreaName(area: any): string {
    const currentLang = this.translationService.getCurrentLanguage();
    return currentLang === 'ar' ? (area.name_ar || area.name_en) : area.name_en;
  }

  // Helper method to get formatted progress text
  getProgressText(): string {
    const template = this.getTranslatedText('STEP_PROGRESS');
    return template
      .replace('{current}', this.currentStep.toString())
      .replace('{total}', this.totalSteps.toString());
  }

  ngOnInit(): void {
    const user = localStorage.getItem('currentUser');
    this.brokerId = user ? JSON.parse(user).brokerId : null;
    this.initForms();
    this.loadUnitTypes();
    this.loadCities();
    this.loadAreas();

     this.filteredUnitTypes = [];
  }

  initForms() {
    // Step 0: Property Category Selection
    this.step0Form = this.fb.group({
      compoundType: ['', [Validators.required]],
      unitOperation: ['', [Validators.required]],
      type: ['', [Validators.required]],
    });

    // Step 1: Basic Property Settings
    this.step1Form = this.fb.group({
      cityId: ['', [Validators.required]],
      areaId: ['', [Validators.required]],
      subAreaId: [''], // Sub area field (optional)
      mallName: ['', [Validators.maxLength(255)]],
      compoundName: ['', [Validators.maxLength(255)]],
      detailedAddress: ['', [Validators.required, Validators.maxLength(255)]],
      location: ['', [ Validators.pattern('https?://.+')]],
      ownerName: ['', Validators.required],
      ownerPhone: [
        '',
        [Validators.required, Validators.pattern('^01[0-2,5]{1}[0-9]{8}$')],
      ],
    });

    // Step 2: Unit Information
    this.step2Form = this.fb.group({
      buildingNumber: ['', [Validators.maxLength(50)]],
      unitNumber: ['', [Validators.maxLength(50)]],
      floor: ['', [Validators.required]],
      unitArea: [
        '',
        [
          Validators.required,
          Validators.min(1),
          Validators.pattern('^[0-9]*$'),
        ],
      ],
      buildingArea: ['', [Validators.min(0), Validators.pattern('^[0-9]*$')]],
      groundArea: ['', [Validators.min(0), Validators.pattern('^[0-9]*$')]],
      numberOfRooms: [
        '',
        [
          Validators.required,
          Validators.min(0),
          Validators.pattern('^[0-9]*$'),
        ],
      ],
      numberOfBathrooms: [
        '',
        [
          Validators.required,
          Validators.min(0),
          Validators.pattern('^[0-9]*$'),
        ],
      ],
      numberOfFloors: [
        '',
        [
          Validators.required,
          Validators.min(0),
          Validators.pattern('^[0-9]*$'),
        ],
      ],
      unitFacing: [''],
      view: ['', [Validators.required]],
      finishingType: ['', [Validators.required]],
      fitOutCondition: [''],
      furnishingStatus: [''],
      groundLayoutStatus: [''],
      unitDesign: [''],
      activity: [''],
      deliveryStatus: ['', [Validators.required]],
      deliveryDate: [''],
      buildingDeadline: [''], // Building deadline field
      financialStatus: [''], // Financial status field
      buildingLayoutStatus: [''],
      parkingStatus: [''],
      clubStatus: [''],
      otherAccessories: [''], // Optional field - no validators to avoid blocking navigation
      legalStatus: [''], // Moved from step5Form
      unitDescription: ['', [Validators.maxLength(1000)]], // Unit description field
    });

    // Step 3: Financial Information
    this.step3Form = this.fb.group({
      requestedOver: ['', [Validators.maxLength(255)]], // Requested over field (optional)
      rentRecurrence: [''], // Rent recurrence field (monthly, daily, annually)
      dailyRent: ['', [Validators.min(0)]], // Daily rent amount
      monthlyRent: ['', [Validators.min(0)]], // Monthly rent amount
      paymentSystem: ['', Validators.required],
      pricePerMeterInInstallment: ['', [Validators.min(0)]],
      totalPriceInInstallment: ['', [Validators.min(0)]],
      pricePerMeterInCash: ['', [Validators.min(0)]],
      totalPriceInCash: ['', [Validators.min(0)]],
    });

    // Step 4: Project Documents
    this.step4Form = this.fb.group({
      diagram: [[]],
      layout: [[]],
      videos: [[]],
      locationInMasterPlan: [[]],
    });

    // Step 5: Owner Information
    this.step5Form = this.fb.group({
      // legalStatus moved to step2Form
    });
  }

  setupInstallmentPriceSync() {
  this.step3Form.get('pricePerMeterInInstallment')?.valueChanges.subscribe((pricePerMeter) => {
    const area = this.step2Form.get('unitArea')?.value;
    if (pricePerMeter && area) {
      const total = pricePerMeter * area;
      this.step3Form.get('totalPriceInInstallment')?.setValue(total, { emitEvent: false });
    }
  });

  this.step3Form.get('totalPriceInInstallment')?.valueChanges.subscribe((totalPrice) => {
    const area = this.step2Form.get('unitArea')?.value;
    if (totalPrice && area) {
      const pricePerMeter = totalPrice / area;
      this.step3Form.get('pricePerMeterInInstallment')?.setValue(pricePerMeter, { emitEvent: false });
    }
  });
}

  setupCashPriceSync() {
  this.step3Form.get('pricePerMeterInCash')?.valueChanges.subscribe((pricePerMeter) => {
    const area = this.step2Form.get('unitArea')?.value;
    if (pricePerMeter && area) {
      const total = pricePerMeter * area;
      this.step3Form.get('totalPriceInCash')?.setValue(total, { emitEvent: false });
    }
  });

  this.step3Form.get('totalPriceInCash')?.valueChanges.subscribe((totalPrice) => {
    const area = this.step2Form.get('unitArea')?.value;
    if (totalPrice && area) {
      const pricePerMeter = totalPrice / area;
      this.step3Form.get('pricePerMeterInCash')?.setValue(pricePerMeter, { emitEvent: false });
    }
  });
}

  // Get current form based on step
  getCurrentForm(): FormGroup {
    switch (this.currentStep) {
      case 0:
        return this.step0Form;
      case 1:
        return this.step1Form;
      case 2:
        return this.step2Form;
      case 3:
        return this.step3Form;
      case 4:
        return this.step4Form;
      case 5:
        return this.step5Form;
      default:
        return this.step0Form;
    }
  }

  loadUnitTypes(): void {
    this.propertyService.getUnitTypes().subscribe({
      next: (response) => {
        this.allUnitTypes = Object.entries(response.data).map(([key, value]) => ({
          key,
          value: value as string,
        }));
        // this.insideCompoundUnitTypes = this.allUnitTypes;
        console.log('Raw API Response:', this.allUnitTypes);
      },
      error: (err) => {
        console.error('Error loading unitTypes:', err);
      },
      complete: () => {
        this.cdr.detectChanges();
      },
    });
  }

  filterUnitTypes(): void {
    const compoundType = this.step0Form.get('compoundType')?.value;

    if (compoundType === 'outside_compound') {
      this.filteredUnitTypes = this.outsideCompoundUnitTypes;
    } else if (compoundType === 'inside_compound') {
      this.filteredUnitTypes = this.insideCompoundUnitTypes;
    } else if (compoundType === 'village') {
       this.filteredUnitTypes = this.RentalUnitTypes;
    } else {
      this.filteredUnitTypes = [];
    }

    this.step0Form.patchValue({ type: '' });
    this.selectedUnitType = '';

    this.cdr.detectChanges();
  }

  loadCities(): void {
    this.isLoadingCities = true;
    this.propertyService.getCities().subscribe({
      next: (response) => {
        if (response && response.data) {
          this.cities = response.data;
        } else {
          console.warn('No cities data in response');
          this.cities = [];
        }
      },
      error: (err) => {
        console.error('Error loading cities:', err);
      },
      complete: () => {
        this.isLoadingCities = false;
        this.cdr.detectChanges();
      },
    });
  }

  loadAreas(cityId?: number): void {
    this.propertyService.getAreas(cityId).subscribe({
      next: (response) => {
        if (response && response.data) {
          this.areas = response.data;
        } else {
          console.warn('No areas data in response');
          this.areas = [];
        }
      },
      error: (err) => {
        console.error('Error loading areas:', err);
        this.areas = [];
      },
      complete: () => {
        this.cdr.detectChanges();
      },
    });
  }

  loadSubAreas(areaId?: number): void {
    this.propertyService.getSubAreas(areaId).subscribe({
      next: (response) => {
        if (response && response.data) {
          this.subAreas = response.data;
        } else {
          console.warn('No sub-areas data in response');
          this.subAreas = [];
        }
      },
      error: (err) => {
        console.error('Error loading sub-areas:', err);
        this.subAreas = [];
      },
      complete: () => {
        this.cdr.detectChanges();
      },
    });
  }

  //**************************************************************** */

  // STEP 0
  filterPropertyTypes(): { key: string; value: string }[] {
  const compoundType = this.step0Form.get('compoundType')?.value;

  if (compoundType === 'village') {
     return this.propertyTypeOptions.filter(option => option.value === 'rent_out');
  }

   return this.propertyTypeOptions;
}


  //******************** */

// STEP 2
getFieldsToShow(): any[] {
  const compoundType = this.step0Form.get('compoundType')?.value;
  const type = this.step0Form.get('type')?.value;
console.log(compoundType, type);
console.log(compoundType === 'outside_compound' &&  (type == 'villas' || type == 'full_buildings') );
  // For outside compound apartments
  if (compoundType === 'outside_compound' &&  (type === 'apartments' ||type === 'duplexes' || type === 'studios' || type === 'penthouses'|| type === 'roofs'|| type === 'basements')) {
    return [ 'buildingNumber', 'unitNumber', 'floor', 'unitArea' ,'numberOfRooms', 'numberOfBathrooms', 'unitFacing', 'view', 'finishingType','deliveryStatus', 'legalStatus', 'otherAccessories',  'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment' ];
  }
  else if (compoundType === 'outside_compound' &&  (type == 'villas' || type == 'full_buildings')) {
    return ['buildingNumber', 'numberOfFloors', 'buildingArea', 'groundArea', 'buildingLayoutStatus', 'unitDescription', 'unitDesign', 'unitFacing', 'view', 'finishingType', 'legalStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];
  }
  else if (compoundType === 'outside_compound' &&  (type === 'pharmacies' ||type === 'medical_clinics' || type === 'administrative_units' || type === 'commercial_stores' )) {
   return ['mallName', 'buildingNumber', 'unitNumber', 'floor', 'unitArea', 'view', 'finishingType', 'fitOutCondition','deliveryStatus','activity' ,'financialStatus', 'otherAccessories' , 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment' ];
  }
  else if (compoundType === 'outside_compound' &&  (type === 'warehouses' || type === 'factories' )) {
  return ['buildingNumber', 'numberOfFloors', 'groundArea', 'buildingArea', 'activity', 'buildingLayoutStatus', 'fitOutCondition', 'unitDescription', 'legalStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];
  }
  else if (compoundType === 'outside_compound' &&  (type === 'residential_villa_lands' || type === 'residential_lands' || type === 'administrative_lands' ||type === 'commercial_administrative_lands' || type === 'commercial_lands' || type === 'medical_lands' || type === 'mixed_lands' || type === 'warehouses_land' || type === 'industrial_lands' )) {
  return [ 'unitNumber','groundArea', 'fitOutCondition', 'unitDescription', 'buildingDeadline', 'view', 'buildingLayoutStatus','legalStatus', 'deliveryStatus', 'financialStatus', 'otherAccessories' ,'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment' ];
  }

  else if (compoundType === 'inside_compound' &&  (type === 'apartments' ||type === 'duplexes' || type === 'studios' || type === 'penthouses'|| type === 'i_villa' )) {
  return ['compoundName', 'buildingNumber', 'unitNumber', 'floor', 'unitArea' ,'numberOfRooms' , 'numberOfBathrooms',  'view', 'finishingType','deliveryStatus', 'financialStatus', 'parkingStatus', 'clubStatus', 'otherAccessories' , 'requestedOver', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment' ];
  }
  else if (compoundType === 'inside_compound' &&  (type === 'standalone_villas' || type ==='twin_houses' || type === 'town_houses')) {
  return ['compoundName', 'buildingNumber', 'numberOfFloors', 'buildingArea', 'groundArea', 'numberOfRooms', 'numberOfBathrooms', 'view', 'finishingType', 'deliveryStatus', 'financialStatus', 'otherAccessories', 'requestedOver', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];
  }

  else if (compoundType === 'inside_compound' &&  (type === 'pharmacies' || type === 'medical_clinics' || type === 'administrative_units' || type === 'commercial_stores' )) {
  return ['compoundName', 'mallName', 'buildingNumber', 'unitNumber', 'floor', 'unitArea','view', 'finishingType', 'activity', 'deliveryStatus','fitOutCondition', 'financialStatus', 'otherAccessories', 'requestedOver', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];
 }
  else if (
  compoundType === 'village' &&
  (
    type === 'apartments' || type === 'duplexes' || type === 'studios' ||
    type === 'penthouses' || type === 'basements' || type === 'roofs' ||
    type === 'i_villa' || type === 'villas' || type === 'standalone_villas' ||
    type === 'full_buildings' || type === 'twin_houses' || type === 'town_houses' ||
    type === 'chalets'
  )
) {
  return [
    'buildingNumber', 'unitNumber', 'unitArea', 'floor', 'view',
    'numberOfRooms', 'numberOfBathrooms', 'finishingType', 'furnishingStatus',
    'otherAccessories', 'rentRecurrence', 'dailyRent', 'monthlyRent', 'annualRent'
  ];
}
else if (
  compoundType === 'village' && (
  type === 'warehouses' || type === 'factories' || type === 'administrative_units' ||
    type === 'medical_clinics' || type === 'commercial_stores' || type === 'pharmacies'
  )
) {
  return [
    'buildingNumber', 'unitNumber', 'unitArea', 'floor', 'view',
    'numberOfRooms', 'numberOfBathrooms', 'finishingType', 'furnishingStatus',
    'otherAccessories', 'rentRecurrence', 'activity', 'dailyRent', 'monthlyRent'
  ];
}

  return [ ];
}

// Check if a specific field should be shown
shouldShowField(fieldName: string): boolean {
  const unitOperation = this.step0Form.get('unitOperation')?.value;
  if (unitOperation === 'rent_out' && (fieldName === 'deliveryStatus' || fieldName === 'legalStatus')) {
    return false;
  }
  if (unitOperation === 'rent_out' && (fieldName === 'furnishingStatus' || fieldName === 'dailyRent' || fieldName === 'monthlyRent' || fieldName === 'rentRecurrence' )) {
    return true;
  }
  return this.getFieldsToShow().includes(fieldName);
}


  // /*/***************************** */
  selectCity(cityId: number, cityName: string) {
    this.selectedCityId = cityId;
    this.selectedCityName = cityName;
    this.step1Form.patchValue({
      cityId: cityId,
    });
    this.loadAreas(cityId);
  }

  selectUnitType(UnitValue: string) {
    this.selectedUnitType = UnitValue;
    this.step0Form.patchValue({
      type: UnitValue,
    });

    // Clear unitFacing field if unit type doesn't require it
    const unitTypesWithFacing = [
      'apartments',
      'duplexes',
      'studios',
      'penthouses',
    ];
    if (!unitTypesWithFacing.includes(UnitValue)) {
      this.step2Form.patchValue({
        unitFacing: null,
      });
    }

    // Clear buildingArea and groundArea fields if unit type doesn't require them
    const unitTypesWithAreaFields = [
      'standalone_villas',
      'factory_lands',
      'commercial_administrative_buildings',
      'residential_buildings',
      'warehouses',

    ];
    if (!unitTypesWithAreaFields.includes(UnitValue)) {
      this.step2Form.patchValue({
        buildingArea: null,
        groundArea: null,
      });
    }

    // Clear activity field if unit type doesn't require it
    const unitTypesWithActivity = [
      'commercial_stores',
      'factory_lands',
      'warehouses',
      'commercial_administrative_buildings',
    ];
    if (!unitTypesWithActivity.includes(UnitValue)) {
      this.step2Form.patchValue({
        activity: null,
      });
    }

    // Clear groundLayoutStatus field if unit type doesn't require it
    const unitTypesWithGroundLayout = [
      'factory_lands',
      'warehouses',
      'residential_buildings',
      'commercial_administrative_buildings',
    ];
    if (!unitTypesWithGroundLayout.includes(UnitValue)) {
      this.step2Form.patchValue({
        groundLayoutStatus: null,
      });
    }

    // Clear unitDesign field if unit type doesn't require it
    const unitTypesWithUnitDesign = ['standalone_villas'];
    if (!unitTypesWithUnitDesign.includes(UnitValue)) {
      this.step2Form.patchValue({
        unitDesign: null,
      });
    }

    // Clear fitOutCondition field if unit type doesn't require it
    const unitTypesWithFitOutCondition = [
      'villas',
      'full_buildings',
      'pharmacies',
      'factory_lands',
      'warehouses',
      'commercial_stores',
      'commercial_administrative_buildings',
    ];
    if (!unitTypesWithFitOutCondition.includes(UnitValue)) {
      this.step2Form.patchValue({
        fitOutCondition: null,
      });
    }

    // Clear furnishingStatus field if unit type doesn't require it
    const unitTypesToHideFurnishing = [
      'pharmacies',
      'commercial_stores',
      'factory_lands',
      'warehouses',
      'commercial_administrative_buildings',
      'administrative_units',
    ];
    if (unitTypesToHideFurnishing.includes(UnitValue)) {
      this.step2Form.patchValue({
        furnishingStatus: null,
      });
    }

    // Clear legalStatus field if unit type doesn't require it
    const unitTypesWithLegalStatus = [
      'duplexes',
      'penthouses',
      'basements',
      'roofs',
    ];
    if (!unitTypesWithLegalStatus.includes(UnitValue)) {
      this.step2Form.patchValue({
        legalStatus: null,
      });
    }
  }

  selectArea(areaId: number, areaName: string) {
    this.selectedAreaName = areaName;
    this.step1Form.patchValue({
      areaId: areaId,
    });
    this.loadSubAreas(areaId);
  }

  selectSubArea(subAreaId: number, subAreaName: string) {
    this.selectedSubAreaName = subAreaName;
    this.step1Form.patchValue({
      subAreaId: subAreaId,
    });
  }

  // dropdown values for step 2
  selectStep2Value(fieldName: string, value: string) {
    this.step2Form.patchValue({
      [fieldName]: value,
    });
  }

  //dropdown values for step 3
  selectStep3Value(fieldName: string, value: string) {
    this.step3Form.patchValue({
      [fieldName]: value,
    });

    // Clear price fields when payment system changes
    if (fieldName === 'paymentSystem') {
      this.clearPriceFields();
    }
  }

  // Clear all price fields when payment system changes
  clearPriceFields() {
    this.step3Form.patchValue({
      pricePerMeterInCash: null,
      totalPriceInCash: null,
      pricePerMeterInInstallment: null,
      totalPriceInInstallment: null,
    });
  }

  // Check if cash price fields should be displayed
  shouldShowCashFields(): boolean {
    const paymentSystem = this.step3Form.get('paymentSystem')?.value;
    return (
      paymentSystem === 'cash' ||
      paymentSystem === 'all_of_the_above_are_suitable'
    );
  }

  // Check if installment price fields should be displayed
  shouldShowInstallmentFields(): boolean {
    const paymentSystem = this.step3Form.get('paymentSystem')?.value;
    return (
      paymentSystem === 'installment' ||
      paymentSystem === 'all_of_the_above_are_suitable'
    );
  }

  // Check if unitFacing field should be displayed
  shouldShowUnitFacingField(): boolean {
    const unitType = this.step0Form.get('type')?.value;
    const unitTypesWithFacing = [
      'apartments',
      'duplexes',
      'studios',
      'penthouses',
    ];
    return unitTypesWithFacing.includes(unitType);
  }

  // Check if buildingArea and groundArea fields should be displayed
  shouldShowAreaFields(): boolean {
    const unitType = this.step0Form.get('type')?.value;
    const unitTypesWithAreaFields = [
      'standalone_villas',
      'factory_lands',
      'commercial_administrative_buildings',
      'residential_buildings',
      'warehouses',
    ];
    return unitTypesWithAreaFields.includes(unitType);
  }

  // Check if groundLayoutStatus field should be displayed
  shouldShowGroundLayoutStatusField(): boolean {
    const unitType = this.step0Form.get('type')?.value;
    const unitTypesWithGroundLayout = [
      'factory_lands',
      'warehouses',
      'residential_buildings',
      'commercial_administrative_buildings',
    ];
    return unitTypesWithGroundLayout.includes(unitType);
  }

  // Check if activity field should be displayed
  shouldShowActivityField(): boolean {
    const unitType = this.step0Form.get('type')?.value;
    const unitTypesWithActivity = [
      'commercial_stores',
      'factory_lands',
      'warehouses',
      'commercial_administrative_buildings',
    ];
    return unitTypesWithActivity.includes(unitType);
  }

  // Check if unitDesign field should be displayed
  shouldShowUnitDesignField(): boolean {
    const unitType = this.step0Form.get('type')?.value;
    const unitTypesWithUnitDesign = ['standalone_villas'];
    return unitTypesWithUnitDesign.includes(unitType);
  }

  // Check if legalStatus field should be displayed
  shouldShowLegalStatusField(): boolean {
    const unitType = this.step0Form.get('type')?.value;
    const unitTypesWithLegalStatus = [
      'duplexes',
      'penthouses',
      'basements',
      'roofs',
    ];
    return unitTypesWithLegalStatus.includes(unitType);
  }

  // Check if fitOutCondition field should be displayed
  shouldShowFitOutConditionField(): boolean {
    const unitType = this.step0Form.get('type')?.value;
    const unitTypesWithFitOutCondition = [
      'villas',
      'full_buildings',
      'pharmacies',
      'factory_lands',
      'warehouses',
      'commercial_stores',
      'commercial_administrative_buildings',
    ];
    return unitTypesWithFitOutCondition.includes(unitType);
  }

  // Check if furnishingStatus field should be displayed
  shouldShowFurnishingStatusField(): boolean {
    const unitType = this.step0Form.get('type')?.value;
    const unitTypesToHideFurnishing = [
      'pharmacies',
      'commercial_stores',
      'factory_lands',
      'warehouses',
      'commercial_administrative_buildings',
      'administrative_units',
    ];
    return !unitTypesToHideFurnishing.includes(unitType);
  }

  // dropdown values for step 0
  selectStep0Value(fieldName: string, value: string) {
    console.log('selectStep0Value called:', { fieldName, value });

    this.step0Form.patchValue({
      [fieldName]: value,
    });

    // Filter unit types when compound type changes
    if (fieldName === 'compoundType') {
      this.filterUnitTypes();
    }

    // Handle unit type selection
    if (fieldName === 'type') {
      this.selectedUnitType = value;
      this.selectUnitType(value); // Call existing logic for unit type selection
    }

    console.log('Step0 form after update:', this.step0Form.value);

    // Trigger change detection to update button state
    this.cdr.detectChanges();
  }

  //dropdown values for step 5
  selectStep5Value(fieldName: string, value: string) {
    this.step5Form.patchValue({
      [fieldName]: value,
    });
  }

  submitForm(checkAd : boolean) {
    if (this.isCurrentFormValid()) {

      // console.log(formData);
      const httpFormData = new FormData();

      // Add step0 form data
      Object.keys(this.step0Form.value).forEach((key) => {
        httpFormData.append(key, this.step0Form.value[key]);
      });

      // Add step1 form data
      Object.keys(this.step1Form.value).forEach((key) => {
        httpFormData.append(key, this.step1Form.value[key]);
      });

      // Fields to be included in additionalDetails array (all fields without conditions)
      const additionalDetailsFields = [
        'groundArea',
        'buildingArea',
        'activity',
        'fitOutCondition',
        'furnishingStatus',
        'groundLayoutStatus',
        'unitDesign',
        'unitFacing',
        'legalStatus',
        'numberOfFloors',
        'buildingDeadline',
        'unitDescription',
        'financialStatus',
        'parkingStatus',
        'clubStatus'
      ];

      // Get unit type for reference
      const unitType = this.step0Form.get('type')?.value;

      // Add step2 form data (excluding fields that go to additionalDetails and otherAccessories)
      Object.keys(this.step2Form.value).forEach((key) => {
        if (
          key !== 'otherAccessories' &&
          !additionalDetailsFields.includes(key)
        ) {
          httpFormData.append(key, this.step2Form.value[key]);
        }
      });

      // Add step3 form data (conditionally based on payment system)
      const paymentSystem = this.step3Form.get('paymentSystem')?.value;

      // Always add payment system
      httpFormData.append('paymentSystem', paymentSystem);

      // Add other step3 fields that should always be sent
      const requestedOver = this.step3Form.get('requestedOver')?.value;
      if (requestedOver) {
        httpFormData.append('requestedOver', requestedOver);
      }

      const dailyRent = this.step3Form.get('dailyRent')?.value;
      if (dailyRent) {
        httpFormData.append('dailyRent', dailyRent);
      }

      const monthlyRent = this.step3Form.get('monthlyRent')?.value;
      if (monthlyRent) {
        httpFormData.append('monthlyRent', monthlyRent);
      }

      // Conditionally add price fields based on payment system
      if (paymentSystem === 'cash') {
        // Only send cash price fields
        const pricePerMeterInCash = this.step3Form.get(
          'pricePerMeterInCash'
        )?.value;
        const totalPriceInCash = this.step3Form.get('totalPriceInCash')?.value;

        if (pricePerMeterInCash) {
          httpFormData.append('pricePerMeterInCash', pricePerMeterInCash);
        }
        if (totalPriceInCash) {
          httpFormData.append('totalPriceInCash', totalPriceInCash);
        }
      } else if (paymentSystem === 'installment') {
        // Only send installment price fields
        const pricePerMeterInInstallment = this.step3Form.get(
          'pricePerMeterInInstallment'
        )?.value;
        const totalPriceInInstallment = this.step3Form.get(
          'totalPriceInInstallment'
        )?.value;

        if (pricePerMeterInInstallment) {
          httpFormData.append(
            'pricePerMeterInInstallment',
            pricePerMeterInInstallment
          );
        }
        if (totalPriceInInstallment) {
          httpFormData.append(
            'totalPriceInInstallment',
            totalPriceInInstallment
          );
        }
      } else if (paymentSystem === 'all_of_the_above_are_suitable') {
        // Send all price fields
        const pricePerMeterInCash = this.step3Form.get(
          'pricePerMeterInCash'
        )?.value;
        const totalPriceInCash = this.step3Form.get('totalPriceInCash')?.value;
        const pricePerMeterInInstallment = this.step3Form.get(
          'pricePerMeterInInstallment'
        )?.value;
        const totalPriceInInstallment = this.step3Form.get(
          'totalPriceInInstallment'
        )?.value;

        if (pricePerMeterInCash) {
          httpFormData.append('pricePerMeterInCash', pricePerMeterInCash);
        }
        if (totalPriceInCash) {
          httpFormData.append('totalPriceInCash', totalPriceInCash);
        }
        if (pricePerMeterInInstallment) {
          httpFormData.append(
            'pricePerMeterInInstallment',
            pricePerMeterInInstallment
          );
        }
        if (totalPriceInInstallment) {
          httpFormData.append(
            'totalPriceInInstallment',
            totalPriceInInstallment
          );
        }
      }

      // Add step5 form data (excluding legalStatus which goes to additionalDetails)
      Object.keys(this.step5Form.value).forEach((key) => {
        if (key !== 'legalStatus') {
          httpFormData.append(key, this.step5Form.value[key]);
        }
      });

      // Create additionalDetails object
      const additionalDetails: any = {};

      // Add fields from step2Form
      additionalDetailsFields.forEach((field) => {
        const value = this.step2Form.get(field)?.value;
        if (value) {
          additionalDetails[field] = value;
        }
      });

      // Add rentRecurrence from step3Form if it exists
      const rentRecurrence = this.step3Form.get('rentRecurrence')?.value;
      if (rentRecurrence) {
        additionalDetails['rentRecurrence'] = rentRecurrence;
      }

      // Add mallName from step1Form if it exists
      const mallName = this.step1Form.get('mallName')?.value;
      if (mallName) {
        additionalDetails['mallName'] = mallName;
      }

      // Send additionalDetails as individual form fields (not JSON)
      Object.keys(additionalDetails).forEach((key) => {
        httpFormData.append(
          `additionalDetails[${key}]`,
          additionalDetails[key]
        );
      });

      //add files
      const fileFields = [
        'diagram',
        'layout',
        'videos',
        'locationInMasterPlan',
      ];
      fileFields.forEach((field) => {
        const files = this.step4Form.get(field)?.value;
        if (files && files.length) {
          const isMultiple = ['layout', 'videos'].includes(field);

          if (isMultiple) {
            files.forEach((file: File) => {
              httpFormData.append(`${field}[]`, file);
            });
          } else {
            httpFormData.append(field, files[0]);
          }
        }
      });

      // Handle otherAccessories as array
      const accessoriesRaw = this.step2Form.get('otherAccessories')?.value;
      const accessoriesArray = Array.isArray(accessoriesRaw)
        ? accessoriesRaw
        : [];

      // Send otherAccessories as individual array elements
      accessoriesArray.forEach((accessory, index) => {
        httpFormData.append(`otherAccessories[${index}]`, accessory);
      });

      httpFormData.append('brokerId', this.brokerId.toString());

      // Set as advertisement
      if(checkAd){
        httpFormData.append('isAdvertisement', '1');
      }

      // Set loading state based on button type
      if (checkAd) {
        this.isSubmittingPublish = true;
      } else {
        this.isSubmittingDraft = true;
      }

      this.propertyService.createProperty(httpFormData).subscribe({
        next: async (response) => {
          console.log('Property data submitted:', response);

          // Success message
          await Swal.fire({
            title: this.getTranslatedText('SUCCESS'),
            text: checkAd ? this.getTranslatedText('PROPERTY_PUBLISHED_SUCCESS') : this.getTranslatedText('PROPERTY_ADDED_SUCCESS'),
            icon: 'success',
            confirmButtonText: this.getTranslatedText('OK')

          });
           this.isSubmittingDraft = false;
          this.isSubmittingPublish = false;


          this.router.navigate(['/broker/dataandproperties'], {
            queryParams: { success: 'add' },
          });
        },
        error: (err) => {
          console.error('Error submitting property:', err);

          // Format error message
          let errorMessage = 'An error occurred while submitting the property.';

          if (err.error) {
            if (typeof err.error === 'string') {
              errorMessage = err.error;
            } else if (err.error.message) {
              errorMessage = err.error.message;
            } else if (err.error.errors) {
              // Handle validation errors
              const errors = Object.values(err.error.errors);
              errorMessage = errors.join('\n');
            }
          } else if (err.message) {
            errorMessage = err.message;
          }

          // Show error message
          Swal.fire({
            title: this.getTranslatedText('ERROR'),
            text: errorMessage,
            icon: 'error',
            confirmButtonText: this.getTranslatedText('OK')
          }).then(() => {
            // Remove loading state when user clicks OK
            this.isSubmittingDraft = false;
            this.isSubmittingPublish = false;
          });
        },
        complete: () => {
          // Complete block - loading states handled in success/error blocks
        }
      });
    }
  }

  cancel() {
    Swal.fire({
      title: this.getTranslatedText('CONFIRM'),
      text: this.getTranslatedText('CONFIRM_CANCEL'),
      html: this.getTranslatedText('UNSAVED_CHANGES'),
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: this.getTranslatedText('YES_CANCEL'),
      cancelButtonText: this.getTranslatedText('NO_STAY'),
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6'
    }).then((result) => {
      if (result.isConfirmed) {
        this.router.navigate(['/broker/dataandproperties']);
      }
    });
  }

  onFileChange(event: any, fieldName: string) {
    if (event.target.files && event.target.files.length) {
      const files = Array.from(event.target.files);
      this.step4Form.patchValue({
        [fieldName]: files,
      });

      console.log(`${fieldName}: ${files.length} files selected`);
    }
  }

  getFileCount(fieldName: string): number {
    const files = this.step4Form.get(fieldName)?.value;
    return files && Array.isArray(files) ? files.length : 0;
  }

  // Check if current form is valid
  isCurrentFormValid(): boolean {
    const currentForm = this.getCurrentForm();

    // For step 0, only check if unit type is selected
    if (this.currentStep === 0) {
      const compoundType = this.step0Form.get('compoundType')?.value;
      const unitType = this.step0Form.get('type')?.value;
      const isValid = !!(compoundType && unitType);
      console.log('Step 0 validation:', { compoundType, unitType, isValid });
      return isValid;
    }

    // For step 2, check only visible/required fields
    if (this.currentStep === 2) {
      return this.isStep2FormValid();
    }

    // For step 3, check only visible/required fields
    if (this.currentStep === 3) {
      return this.isStep3FormValid();
    }

    return currentForm.valid;
  }

  // Custom validation for Step 2 - only check visible fields
  isStep2FormValid(): boolean {
    const form = this.step2Form;
    const fieldsToShow = this.getFieldsToShow();

    // Required fields that must always be valid if they're shown
    const requiredFields = ['unitArea', 'numberOfRooms', 'numberOfBathrooms', 'view', 'finishingType'];


    for (const fieldName of requiredFields) {
      if (fieldsToShow.includes(fieldName)) {
        const control = form.get(fieldName);
        if (!control || control.invalid) {
          console.log(`Step 2 validation failed for field: ${fieldName}`, control?.errors);
          return false;
        }
      }
    }

    // Check conditional fields only if they're shown and required for the specific unit type
    const conditionalFields = ['unitFacing', 'legalStatus', 'fitOutCondition', 'furnishingStatus','deliveryStatus', 'groundLayoutStatus', 'unitDesign', 'activity'];

    for (const fieldName of conditionalFields) {
      if (fieldsToShow.includes(fieldName) && this.isFieldRequiredForUnitType(fieldName)) {
        const control = form.get(fieldName);
        if (!control || control.invalid) {
          console.log(`Step 2 validation failed for conditional field: ${fieldName}`, control?.errors);
          return false;
        }
      }
    }

    console.log('Step 2 validation passed');
    return true;
  }

  // Custom validation for Step 3 - only check visible fields
  isStep3FormValid(): boolean {
    const form = this.step3Form;
    const fieldsToShow = this.getFieldsToShow();

    // Required fields for step 3
    const requiredFields = ['paymentSystem'];

    for (const fieldName of requiredFields) {
      if (fieldsToShow.includes(fieldName)) {
        const control = form.get(fieldName);
        if (!control || control.invalid) {
          console.log(`Step 3 validation failed for field: ${fieldName}`, control?.errors);
          return false;
        }
      }
    }

    // Check optional fields only if they're shown
    const optionalFields = ['rentRecurrence', 'dailyRent', 'monthlyRent', 'requestedOver', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];

    for (const fieldName of optionalFields) {
      if (fieldsToShow.includes(fieldName)) {
        const control = form.get(fieldName);
        if (control && control.invalid) {
          console.log(`Step 3 validation failed for optional field: ${fieldName}`, control?.errors);
          return false;
        }
      }
    }

    return true;
  }

  // Check if a field is required for the current unit type
  isFieldRequiredForUnitType(fieldName: string): boolean {
    switch (fieldName) {
      case 'unitFacing':
        return this.shouldShowUnitFacingField();
      case 'legalStatus':
        return this.shouldShowLegalStatusField();
      case 'fitOutCondition':
        return this.shouldShowFitOutConditionField();
      case 'furnishingStatus':
        return this.shouldShowFurnishingStatusField();
      case 'groundLayoutStatus':
        return this.shouldShowGroundLayoutStatusField();
      case 'unitDesign':
        return this.shouldShowUnitDesignField();
      case 'activity':
        return this.shouldShowActivityField();
      default:
        return false;
    }
  }

  // Navigate to next step
  nextStep() {
    if (this.currentStep < this.totalSteps) {
      if(this.currentStep == 2){
        this.setupCashPriceSync();
        this.setupInstallmentPriceSync();
      }
      this.currentStep++;
    }
  }

  // Navigate to previous step
  prevStep() {
    if (this.currentStep > 0) {
      this.currentStep--;
    }
  }

  formatUnitTypeKey(key: string): string {
    if (!key || typeof key !== 'string') return '';

    return key
      .split('_')
      .map((word) =>
        word.trim() ? word[0].toUpperCase() + word.slice(1).toLowerCase() : ''
      )
      .join(' ');
  }

  toggleAccessory(value: string): void {
    const index = this.selectedAccessories.indexOf(value);

    if (index > -1) {
      this.selectedAccessories.splice(index, 1);
    } else {
      this.selectedAccessories.push(value);
    }

    // Update form control
    this.step2Form.patchValue({
      otherAccessories: [...this.selectedAccessories],
    });
  }

  // Handle "All The Above Are Suitable" checkbox
  onAllAccessoriesChange(event: any): void {
    if (event.target.checked) {
      // Select all accessories
      this.selectedAccessories = this.otherAccessoriesTypes.map((a) => a.value);
    } else {
      // Unselect all accessories
      this.selectedAccessories = [];
    }

    this.step2Form.patchValue({
      otherAccessories: [...this.selectedAccessories],
    });
  }

  isAccessorySelected(value: string): boolean {
    return this.selectedAccessories.includes(value);
  }

  getSelectedAccessoriesText(): string {
    if (this.selectedAccessories.length === 0) {
      return '';
    }

    if (this.selectedAccessories.length === 1) {
      const accessory = this.otherAccessoriesTypes.find(
        (a) => a.value === this.selectedAccessories[0]
      );
      return accessory ? accessory.key : '';
    }

    return `${this.selectedAccessories.length} accessories selected`;
  }

  // Get compound type text for display
  getCompoundTypeText(value: string): string {
    if (!value) return '';
    const option = this.compoundOptions.find(opt => opt.value === value);
    return option ? option.key : '';
  }



  // Get unit type text for display
  getUnitTypeText(value: string): string {
    if (!value) return '';
    const unitType = this.filteredUnitTypes.find(unit => unit.value === value);
    return unitType ? unitType.key : '';
  }
}

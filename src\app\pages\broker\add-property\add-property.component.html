<div class="mb-5 mt-0">
  <app-broker-title [showCreateButton]="false"></app-broker-title>
</div>

<div class="card rounded-4">
  <div class="card-body p-10">
    <div class="stepper stepper-pills d-flex flex-column" id="add_property_stepper">
      <!-- Header and Progress Bar -->
      <div class="mb-5 text-center">
        <ng-container *ngIf="currentStep === 0">
          <h2>
            <span class="text-dark-blue fw-bold">{{ getTranslatedText('ADD_PROPERTY') }} - </span>
            <span class="text-dark-blue fw-normal">{{ getTranslatedText('PROPERTY_CATEGORY') }}</span>
          </h2>
        </ng-container>
        <ng-container *ngIf="currentStep === 1">
          <h2>
            <span class="text-dark-blue fw-bold">{{ getTranslatedText('ADD_UNIT') }} - </span>
            <span class="text-dark-blue fw-normal">{{ getTranslatedText('LOCATION_INFORMATION') }}</span>
          </h2>
        </ng-container>
        <ng-container *ngIf="currentStep === 2">
          <h2>
            <span class="text-dark-blue fw-bold">{{ getTranslatedText('ADD_UNIT') }} - </span>
            <span class="text-dark-blue fw-normal">{{ getTranslatedText('UNIT_INFORMATION') }}</span>
          </h2>
        </ng-container>
        <ng-container *ngIf="currentStep === 3">
          <h2>
            <span class="text-dark-blue fw-bold">{{ getTranslatedText('ADD_UNIT') }} - </span>
            <span class="text-dark-blue fw-normal">{{ getTranslatedText('PAYMENT_DETAILS') }}</span>
          </h2>
        </ng-container>
        <ng-container *ngIf="currentStep === 4">
          <h2>
            <span class="text-dark-blue fw-bold">{{ getTranslatedText('ADD_PROPERTY') }} - </span>
            <span class="text-dark-blue fw-normal">{{ getTranslatedText('MEDIA_DOCUMENTS') }}</span>
          </h2>
        </ng-container>

        <ng-container *ngIf="currentStep === 5">
          <h2>
            <span class="text-dark-blue fw-bold">{{ getTranslatedText('ADD_PROPERTY') }} - </span>
            <span class="text-dark-blue fw-normal">{{ getTranslatedText('REVIEW_SUBMIT') }}</span>
          </h2>
        </ng-container>

        <div class="d-flex justify-content-center align-items-center mb-2"
          [class.rtl-layout]="translationService.getCurrentLanguage() === 'ar'">
          <span class="text-success fw-bold step-indicator-text">{{ getTranslatedText('STEP') }} {{ currentStep
            }}</span>
          <span class="text-muted mx-1 step-separator">{{ getTranslatedText('OF') }}</span>
          <span class="text-muted step-total">{{ totalSteps }}</span>
        </div>

        <div *ngIf="currentStep > 0" class="text-primary cursor-pointer mb-2 text-center" (click)="prevStep()">
          {{ getTranslatedText('BACK_TO_PREVIOUS_STEP') }}
        </div>

        <div class="progress h-8px bg-light-success w-75 mx-auto">
          <div class="progress-bar bg-success" role="progressbar" [style.width]="(currentStep / totalSteps) * 100 + '%'"
            aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
        </div>
      </div>

      <!-- Form Content -->
      <form class="mx-auto w-100 pt-3 pt-md-5 pb-4 pb-md-6">
        <!-- Step 0: Property Category Selection -->
        <div *ngIf="currentStep === 0" [formGroup]="step0Form" class="px-2 px-md-0">
          <div class="mb-6 mb-md-10">
            <label class="form-label fw-bold text-start d-block">{{ getTranslatedText('COMPOUND_TYPE') }}</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="compoundTypeDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  getTranslatedOption(getCompoundTypeText(step0Form.get("compoundType")?.value)) ||
                  getTranslatedText('SELECT_COMPOUND_TYPE')
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="compoundTypeDropdown" style="
                  max-height: 200px;
                  overflow-y: auto;
                  position: absolute;
                  z-index: 1000;
                ">
                <li *ngFor="let option of compoundOptions">
                  <a class="dropdown-item text-start" (click)="selectStep0Value('compoundType', option.value)">{{
                    getTranslatedOption(option.key) }}</a>
                </li>
              </ul>
            </div>
          </div>

          <!-- Property Type (Sale/Rent) -->
          <div class="mb-6 mb-md-10">
            <label class="form-label fw-bold text-start d-block">{{ getTranslatedText('PROPERTY_TYPE') }}</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="propertyTypeDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  getTranslatedOption(formatUnitTypeKey(step0Form.get("unitOperation")?.value)) ||
                  getTranslatedText('SELECT_PROPERTY_TYPE')
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="propertyTypeDropdown" style="
                  max-height: 200px;
                  overflow-y: auto;
                  position: absolute;
                  z-index: 1000;
                ">
                <li *ngFor="let option of filterPropertyTypes()">
                  <a class="dropdown-item text-start" (click)="selectStep0Value('unitOperation', option.value)">{{
                    getTranslatedOption(option.key) }}</a>
                </li>
              </ul>
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block">{{ getTranslatedText('UNIT_TYPE') }}</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="unitTypeDropdownStep0" data-bs-toggle="dropdown" aria-expanded="false"
                [disabled]="filteredUnitTypes.length === 0">
                <span>{{
                  getTranslatedUnitType(step0Form.get("type")?.value) ||
                  getTranslatedText('SELECT_UNIT_TYPE')
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="unitTypeDropdownStep0" style="
                  max-height: 200px;
                  overflow-y: auto;
                  position: absolute;
                  z-index: 1000;
                ">
                <li *ngFor="let unitType of filteredUnitTypes">
                  <a class="dropdown-item text-start" (click)="selectStep0Value('type', unitType.value)">{{
                    getTranslatedUnitType(unitType.value) || unitType.key
                    }}</a>
                </li>
              </ul>
            </div>
            <small *ngIf="filteredUnitTypes.length === 0" class="text-muted">
              {{ getTranslatedText('SELECT_COMPOUND_TYPE_FIRST') }}
            </small>
          </div>
        </div>

        <!-- Step 1: Basic Property Settings -->
        <div *ngIf="currentStep === 1" [formGroup]="step1Form" class="px-2 px-md-0">
          <!-- Owner Information Row -->
          <div class="row mb-10">
            <!-- Owner Name -->
            <div class="col-md-6">
              <label class="form-label fw-bold text-start d-block">
                {{ getTranslatedText('OWNER_NAME') }}
              </label>
              <input type="text" class="form-control text-start" formControlName="ownerName"
                [placeholder]="getTranslatedText('ENTER_NAME')" />
            </div>

            <!-- Owner Phone -->
            <div class="col-md-6">
              <label class="form-label fw-bold text-start d-block">
                {{ getTranslatedText('OWNER_PHONE') }}
              </label>
              <input type="text" class="form-control text-start" [ngClass]="{
                  'is-invalid':
                    step1Form.get('ownerPhone')?.invalid &&
                    (step1Form.get('ownerPhone')?.touched ||
                      step1Form.get('ownerPhone')?.dirty)
                }" formControlName="ownerPhone" [placeholder]="getTranslatedText('ENTER_PHONE_NUMBER')" />
              <div *ngIf="
                  step1Form.get('ownerPhone')?.invalid &&
                  (step1Form.get('ownerPhone')?.touched ||
                    step1Form.get('ownerPhone')?.dirty)
                " class="invalid-feedback">
                <div *ngIf="step1Form.get('ownerPhone')?.errors?.['required']">
                  {{ getTranslatedText('PHONE_REQUIRED') }}
                </div>
                <div *ngIf="step1Form.get('ownerPhone')?.errors?.['pattern']">
                  {{ getTranslatedText('PHONE_PATTERN_ERROR') }}
                </div>
              </div>
            </div>
          </div>

          <!-- Location Information Row -->
          <div class="row mb-10">
            <!-- City -->
            <div class="col-md-6">
              <label class="form-label fw-bold text-start d-block">{{ getTranslatedText('CITY') }}</label>

              <!-- Loading indicator -->
              <div *ngIf="isLoadingCities" class="text-primary mb-2">
                {{ getTranslatedText('LOADING_CITIES') }}
              </div>

              <!-- Debug info -->
              <div *ngIf="!isLoadingCities && cities.length === 0" class="text-danger mb-2">
                {{ getTranslatedText('NO_CITIES_AVAILABLE') }}
              </div>

              <div class="dropdown">
                <button
                  class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                  type="button" id="cityDropdownStep1" data-bs-toggle="dropdown" aria-expanded="false"
                  [disabled]="isLoadingCities">
                  <span>
                    <ng-container *ngIf="isLoadingCities">
                      {{ getTranslatedText('LOADING') }}
                    </ng-container>
                    <ng-container *ngIf="!isLoadingCities">
                      {{ selectedCityName || getTranslatedText('SELECT_CITY') }}
                    </ng-container>
                  </span>
                  <i class="fas fa-chevron-down"></i>
                </button>

                <ul class="dropdown-menu w-100" aria-labelledby="cityDropdownStep1" style="
                    max-height: 200px;
                    overflow-y: auto;
                    position: absolute;
                    z-index: 1000;
                  ">
                  <!-- Debug info -->
                  <li class="dropdown-item disabled">
                    {{ getTranslatedText('TOTAL_CITIES') }}: {{ cities.length }}
                  </li>

                  <ng-container *ngIf="cities && cities.length > 0; else noCities">
                    <li *ngFor="let city of cities" style="cursor: pointer">
                      <a class="dropdown-item text-start" (click)="selectCity(city.id, getCityName(city))">
                        {{ getCityName(city) }}
                      </a>
                    </li>
                  </ng-container>

                  <ng-template #noCities>
                    <li>
                      <a class="dropdown-item text-start disabled">
                        {{ getTranslatedText('NO_CITIES_AVAILABLE') }}
                      </a>
                    </li>
                  </ng-template>
                </ul>
              </div>
            </div>

            <!-- Area -->
            <div class="col-md-6">
              <label class="form-label fw-bold text-start d-block">{{ getTranslatedText('AREA') }}</label>
              <div class="dropdown">
                <button
                  class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                  type="button" id="areaDropdownStep1" data-bs-toggle="dropdown" aria-expanded="false">
                  <span>{{ selectedAreaName || getTranslatedText('SELECT_AREA') }}</span>
                  <i class="fas fa-chevron-down"></i>
                </button>
                <ul class="dropdown-menu w-100" aria-labelledby="areaDropdownStep1" style="
                    max-height: 200px;
                    overflow-y: auto;
                    position: absolute;
                    z-index: 1000;
                  ">
                  <li *ngIf="areas.length > 0">
                    <a *ngFor="let area of areas" class="dropdown-item text-start"
                      (click)="selectArea(area.id, getAreaName(area))">
                      {{ getAreaName(area) }}
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="mb-10">
                <label class="form-label fw-bold text-start d-block">
                  {{ getTranslatedText('SUB_AREA') }}
                </label>
                <div class="dropdown">
                  <button
                    class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                    type="button" id="subAreaDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <span style="color: #000">{{
                      selectedSubAreaName || getTranslatedText('SELECT_SUB_AREA')
                      }}</span>
                    <i class="fas fa-chevron-down"></i>
                  </button>
                  <ul class="dropdown-menu w-100 areas-dropdown" aria-labelledby="subAreaDropdown">
                    <li class="areas-list">
                      <a *ngFor="let subArea of subAreas" class="dropdown-item text-start text-dark"
                        (click)="selectSubArea(subArea.id, getAreaName(subArea))" style="color: #090909 !important">
                        {{ getAreaName(subArea) }}
                      </a>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-10">
                <label class="form-label fw-bold text-start d-block">
                  {{ getTranslatedText('DETAILED_ADDRESS') }}
                </label>
                <input type="text" class="form-control text-start" formControlName="detailedAddress"
                  [placeholder]="getTranslatedText('ENTER_DETAILED_ADDRESS')" />
              </div>
            </div>
          </div>

          <!-- Mall Name -->
          <div class="mb-10" *ngIf="shouldShowField('mallName')">
            <label class="form-label fw-bold text-start d-block">
              {{ getTranslatedText('MALL_NAME') }}
            </label>
            <input type="text" class="form-control text-start" formControlName="mallName"
              [placeholder]="getTranslatedText('ENTER_MALL_NAME')" [ngClass]="{
                'is-invalid':
                  step1Form.get('mallName')?.invalid &&
                  (step1Form.get('mallName')?.touched ||
                    step1Form.get('mallName')?.dirty)
              }" />
            <div *ngIf="
                step1Form.get('mallName')?.invalid &&
                (step1Form.get('mallName')?.touched ||
                  step1Form.get('mallName')?.dirty)
              " class="invalid-feedback">
              <div *ngIf="step1Form.get('mallName')?.errors?.['maxlength']">
                {{ getTranslatedText('MALL_NAME_MAX_LENGTH') }}
              </div>
            </div>
          </div>

          <!-- Compound Name -->
          <div class="mb-10" *ngIf="shouldShowField('compoundName')">
            <label class="form-label fw-bold text-start d-block">
              {{ getTranslatedText('COMPOUND_NAME') }}
            </label>
            <input type="text" class="form-control text-start" formControlName="compoundName"
              [placeholder]="getTranslatedText('ENTER_COMPOUND_NAME')" [ngClass]="{
                'is-invalid':
                  step1Form.get('compoundName')?.invalid &&
                  (step1Form.get('compoundName')?.touched ||
                    step1Form.get('compoundName')?.dirty)
              }" />
            <div *ngIf="
                step1Form.get('compoundName')?.invalid &&
                (step1Form.get('compoundName')?.touched ||
                  step1Form.get('compoundName')?.dirty)
              " class="invalid-feedback">
              <div *ngIf="step1Form.get('compoundName')?.errors?.['maxlength']">
                {{ getTranslatedText('COMPOUND_NAME_MAX_LENGTH') }}
              </div>
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block">
              {{ getTranslatedText('GOOGLE_MAPS_LINK') }}
            </label>
            <input type="text" class="form-control text-start" [ngClass]="{
                'is-invalid':
                  step1Form.get('location')?.invalid &&
                  (step1Form.get('location')?.touched ||
                    step1Form.get('location')?.dirty)
              }" formControlName="location" [placeholder]="getTranslatedText('ENTER_GOOGLE_MAPS_LINK')" />
            <div *ngIf="
                step1Form.get('location')?.invalid &&
                (step1Form.get('location')?.touched ||
                  step1Form.get('location')?.dirty)
              " class="invalid-feedback">
              <div *ngIf="step1Form.get('location')?.errors?.['required']">
                {{ getTranslatedText('GOOGLE_MAPS_REQUIRED') }}
              </div>
              <div *ngIf="step1Form.get('location')?.errors?.['pattern']">
                {{ getTranslatedText('GOOGLE_MAPS_PATTERN_ERROR') }}
              </div>
            </div>
          </div>
        </div>

        <!-- Step 2: Unit Information -->
        <div *ngIf="currentStep === 2" [formGroup]="step2Form" class="px-2 px-md-0">
          <div class="row mb-10">
            <div class="col-md-6" *ngIf="shouldShowField('buildingNumber')">
              <label class="form-label fw-bold text-start d-block">{{ getTranslatedText('BUILDING_NUMBER') }}</label>
              <input type="text" class="form-control text-start" formControlName="buildingNumber"
                [placeholder]="getTranslatedText('ENTER_BUILDING_NUMBER')" />
            </div>
            <div class="col-md-6" *ngIf="shouldShowField('unitNumber')">
              <label class="form-label fw-bold text-start d-block">{{ getTranslatedText('UNIT_NUMBER') }}</label>
              <input type="text" class="form-control text-start" formControlName="unitNumber"
                [placeholder]="getTranslatedText('ENTER_UNIT_NUMBER')" />
            </div>
          </div>

          <div class="row mb-10">
            <div class="col-md-6" *ngIf="shouldShowField('floor')">
              <label class="form-label fw-bold text-start d-block">{{ getTranslatedText('FLOOR') }}</label>
              <div class="dropdown">
                <button
                  class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                  type="button" id="floorDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                  <span>{{
                    getTranslatedOption(formatUnitTypeKey(step2Form.get("floor")?.value)) ||
                    getTranslatedText('SELECT_FLOOR')
                    }}</span>
                  <i class="fas fa-chevron-down"></i>
                </button>
                <ul class="dropdown-menu w-100" aria-labelledby="floorDropdown">
                  <li *ngFor="let floor of floorTypes">
                    <a class="dropdown-item text-start" (click)="selectStep2Value('floor', floor.value)">{{
                      getTranslatedOption(floor.key) }}</a>
                  </li>
                </ul>
              </div>
            </div>
            <div class="col-md-6" *ngIf="shouldShowField('unitArea')">
              <label class="form-label fw-bold text-start d-block">{{ getTranslatedText('UNIT_AREA_SQM') }}</label>
              <input type="number" class="form-control text-start" formControlName="unitArea"
                [placeholder]="getTranslatedText('ENTER_UNIT_AREA')" min="0" />
            </div>
          </div>

          <div class="row mb-10">
            <div class="col-md-6" *ngIf="shouldShowField('buildingArea')">
              <label class="form-label fw-bold text-start d-block">{{ getTranslatedText('BUILDING_AREA_SQM') }}</label>
              <input type="number" class="form-control text-start" formControlName="buildingArea"
                [placeholder]="getTranslatedText('ENTER_BUILDING_AREA')" min="0" />
            </div>
            <div class="col-md-6" *ngIf="shouldShowField('groundArea')">
              <label class="form-label fw-bold text-start d-block">{{ getTranslatedText('GROUND_AREA_SQM') }}</label>
              <input type="number" class="form-control text-start" formControlName="groundArea"
                [placeholder]="getTranslatedText('ENTER_GROUND_AREA')" min="0" />
            </div>
          </div>

          <div class="row mb-10">
            <div class="col-md-6" *ngIf="shouldShowField('numberOfRooms')">
              <label class="form-label fw-bold text-start d-block">{{ getTranslatedText('NUMBER_OF_ROOMS') }}</label>
              <input type="number" class="form-control text-start" formControlName="numberOfRooms"
                [placeholder]="getTranslatedText('ENTER_NUMBER_OF_ROOMS')" min="0" />
            </div>
            <div class="col-md-6" *ngIf="shouldShowField('numberOfBathrooms')">
              <label class="form-label fw-bold text-start d-block">{{ getTranslatedText('NUMBER_OF_BATHROOMS')
                }}</label>
              <input type="number" class="form-control text-start" formControlName="numberOfBathrooms"
                [placeholder]="getTranslatedText('ENTER_NUMBER_OF_BATHROOMS')" min="0" />
            </div>
          </div>

          <div class="row mb-10" *ngIf="shouldShowField('numberOfFloors')">
            <label class="form-label fw-bold text-start d-block">{{ getTranslatedText('NUMBER_OF_FLOORS') }}</label>
            <input type="number" class="form-control text-start" formControlName="numberOfFloors"
              [placeholder]="getTranslatedText('ENTER_NUMBER_OF_FLOORS')" min="0" />
          </div>

          <!-- Unit Facing Field - Only show for specific unit types -->

          <div class="mb-10" *ngIf="shouldShowField('unitFacing')">
            <label class="form-label fw-bold text-start d-block">{{ getTranslatedText('FACING_LOCATION') }}</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="apartmentLocationDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  getTranslatedOption(formatUnitTypeKey(step2Form.get("unitFacing")?.value)) ||
                  getTranslatedText('SELECT_FACING_LOCATION')
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="apartmentLocationDropdown">
                <li>
                  <a class="dropdown-item text-start" (click)="selectStep2Value('unitFacing', 'right_of_facade')">{{
                    getTranslatedOption('Right Of Facade') }}</a>
                </li>
                <li>
                  <a class="dropdown-item text-start" (click)="selectStep2Value('unitFacing', 'left_of_facade')">
                    {{ getTranslatedOption('Left Of Facade') }}
                  </a>
                </li>
                <li>
                  <a class="dropdown-item text-start" (click)="selectStep2Value('unitFacing', 'side_view')">
                    {{ getTranslatedOption('Side View') }}
                  </a>
                </li>
                <li>
                  <a class="dropdown-item text-start" (click)="selectStep2Value('unitFacing', 'rear_view')">
                    {{ getTranslatedOption('Rear View') }}
                  </a>
                </li>
              </ul>
            </div>
          </div>

          <!-- Building Layout status -->
          <div class="mb-10" *ngIf="shouldShowField('buildingLayoutStatus')">
            <label class="form-label fw-bold text-start d-block">Building Layout Status</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="apartmentLocationDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  formatUnitTypeKey(step2Form.get("buildingLayoutStatus")?.value) ||
                  "Select apartment location"
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="apartmentLocationDropdown">
                <li>
                  <a class="dropdown-item text-start"
                    (click)="selectStep2Value('buildingLayoutStatus', 'open_space')">Open Space</a>
                </li>
                <li>
                  <a class="dropdown-item text-start"
                    (click)="selectStep2Value('buildingLayoutStatus', 'single_apartment')">
                    Single Apartment
                  </a>
                </li>
                <li>
                  <a class="dropdown-item text-start"
                    (click)="selectStep2Value('buildingLayoutStatus', 'two_apartments')">
                    Two Apartments
                  </a>
                </li>
                <li>
                  <a class="dropdown-item text-start"
                    (click)="selectStep2Value('buildingLayoutStatus', 'under_construction')">
                    Under Construction
                  </a>
                </li>
                <li>
                  <a class="dropdown-item text-start"
                    (click)="selectStep2Value('buildingLayoutStatus', 'fully_built_warehouse')">
                    Fully Built
                  </a>
                </li>
                <li>
                  <a class="dropdown-item text-start"
                    (click)="selectStep2Value('buildingLayoutStatus', 'all_acceptable')">
                    All Acceptable
                  </a>
                </li>
              </ul>
            </div>
          </div>

          <!-- Building Layout status
          <div class="mb-10" *ngIf="shouldShowField('buildingLayoutStatus')">
            <label class="form-label fw-bold text-start d-block">Building Layout Status</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="apartmentLocationDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  formatUnitTypeKey(step2Form.get("buildingLayoutStatus")?.value) ||
                  "Select apartment location"
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="apartmentLocationDropdown">
                <li>
                  <a class="dropdown-item text-start" (click)="selectStep2Value('buildingLayoutStatus', 'open_space')">Open Space</a>
                </li>
                <li>
                  <a class="dropdown-item text-start" (click)="selectStep2Value('buildingLayoutStatus', 'single_apartment')">
                    Single Apartment
                  </a>
                </li>
                <li>
                  <a class="dropdown-item text-start" (click)="selectStep2Value('buildingLayoutStatus', 'two_apartments')">
                    Two Apartments
                  </a>
                </li>
                <li>
                  <a class="dropdown-item text-start" (click)="selectStep2Value('buildingLayoutStatus', 'under_construction')">
                    Under Construction
                  </a>
                </li>
                <li>
                  <a class="dropdown-item text-start" (click)="selectStep2Value('buildingLayoutStatus', 'fully_built_warehouse')">
                    Fully Built
                  </a>
                </li>
                <li>
                  <a class="dropdown-item text-start" (click)="selectStep2Value('buildingLayoutStatus', 'all_acceptable')">
                    All Acceptable
                  </a>
                </li>
              </ul>
            </div>
          </div> -->

          <!-- Unit Description Field -->
          <div class="mb-10" *ngIf="shouldShowField('unitDescription')">
            <label class="form-label fw-bold text-start d-block">Unit Description</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="apartmentLocationDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  formatUnitTypeKey(step2Form.get("unitDescription")?.value) ||
                  "Select apartment location"
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="apartmentLocationDropdown">
                <li>
                  <a class="dropdown-item text-start"
                    (click)="selectStep2Value('unitDescription', 'single_front')">Single Front</a>
                </li>
                <li>
                  <a class="dropdown-item text-start" (click)="selectStep2Value('unitDescription', 'corner')">
                    Corner
                  </a>
                </li>
                <li>
                  <a class="dropdown-item text-start" (click)="selectStep2Value('unitDescription', 'double_front')">
                    Double Front
                  </a>
                </li>
                <li>
                  <a class="dropdown-item text-start" (click)="selectStep2Value('unitDescription', 'triple_corner')">
                    Triple Corner
                  </a>
                </li>
                <li>
                  <a class="dropdown-item text-start" (click)="selectStep2Value('unitDescription', 'quad_corner')">
                    Quad Corner
                  </a>
                </li>
                <li>
                  <a class="dropdown-item text-start" (click)="selectStep2Value('unitDescription', 'all_acceptable')">
                    All Acceptable
                  </a>
                </li>
              </ul>
            </div>
          </div>
          <!-- Building Deadline -->
          <div class="mb-10" *ngIf="shouldShowField('buildingDeadline')">
            <label class="form-label fw-bold text-start d-block">{{ getTranslatedText('BUILDING_DEADLINE') }}</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="buildingDeadlineDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  getTranslatedOption(formatUnitTypeKey(step2Form.get("buildingDeadline")?.value)) ||
                  getTranslatedText('SELECT_BUILDING_DEADLINE')
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="buildingDeadlineDropdown">
                <li *ngFor="let deadline of buildingDeadlineTypes">
                  <a class="dropdown-item text-start" (click)="
                      selectStep2Value('buildingDeadline', deadline.value)
                    ">
                    {{ getTranslatedOption(deadline.key) }}
                  </a>
                </li>
              </ul>
            </div>
          </div>

          <div class="mb-10" *ngIf="shouldShowField('view')">
            <label class="form-label fw-bold text-start d-block">{{ getTranslatedText('VIEW') }}</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="apartmentViewDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  getTranslatedOption(formatUnitTypeKey(step2Form.get("view")?.value)) ||
                  getTranslatedText('SELECT_VIEW')
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="apartmentViewDropdown">
                <li *ngFor="let view of viewTypes">
                  <a class="dropdown-item text-start" (click)="selectStep2Value('view', view.value)">
                    {{ getTranslatedOption(view.key) }}
                  </a>
                </li>
              </ul>
            </div>
          </div>

          <div class="mb-10" *ngIf="shouldShowField('finishingType')">
            <label class="form-label fw-bold text-start d-block">{{ getTranslatedText('FINISHING_STATUS') }}</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="finishingStatusDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  getTranslatedOption(formatUnitTypeKey(step2Form.get("finishingType")?.value)) ||
                  getTranslatedText('SELECT_FINISHING_STATUS')
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="finishingStatusDropdown">
                <li *ngFor="let status of finishingType">
                  <a class="dropdown-item text-start" (click)="selectStep2Value('finishingType', status.value)">{{
                    getTranslatedOption(status.key) }}</a>
                </li>
              </ul>
            </div>
          </div>

          <!-- Fit Out Condition Field - Only show for specific unit types -->
          <div class="mb-10" *ngIf="shouldShowField('fitOutCondition')">
            <label class="form-label fw-bold text-start d-block">{{ getTranslatedText('FIT_OUT_CONDITION') }}</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="fitOutConditionDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  getTranslatedOption(formatUnitTypeKey(step2Form.get("fitOutCondition")?.value)) ||
                  getTranslatedText('SELECT_FIT_OUT_CONDITION')
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="fitOutConditionDropdown">
                <li *ngFor="let condition of fitOutConditionTypes">
                  <a class="dropdown-item text-start" (click)="
                      selectStep2Value('fitOutCondition', condition.value)
                    ">{{ getTranslatedOption(condition.key) }}</a>
                </li>
              </ul>
            </div>
          </div>

          <!-- Furnishing Status Field - Only show for specific unit types -->
          <div class="mb-10" *ngIf="
              shouldShowFurnishingStatusField() &&
              shouldShowField('furnishingStatus')
            ">
            <label class="form-label fw-bold text-start d-block">{{ getTranslatedText('FURNISHING_STATUS') }}</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="furnishingStatusDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  getTranslatedOption(formatUnitTypeKey(step2Form.get("furnishingStatus")?.value)) ||
                  getTranslatedText('SELECT_FURNISHING_STATUS')
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="furnishingStatusDropdown">
                <li *ngFor="let furnishing of furnishingStatusTypes">
                  <a class="dropdown-item text-start" (click)="
                      selectStep2Value('furnishingStatus', furnishing.value)
                    ">{{ getTranslatedOption(furnishing.key) }}</a>
                </li>
              </ul>
            </div>
          </div>

          <!-- Ground Layout Status Field - Only show for specific unit types -->
          <div class="mb-10" *ngIf="
              shouldShowGroundLayoutStatusField() &&
              shouldShowField('groundLayoutStatus')
            ">
            <label class="form-label fw-bold text-start d-block">{{ getTranslatedText('GROUND_LAYOUT_STATUS') }}</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="groundLayoutStatusDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  getTranslatedOption(formatUnitTypeKey(
                  step2Form.get("groundLayoutStatus")?.value
                  )) || getTranslatedText('SELECT_GROUND_LAYOUT_STATUS')
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="groundLayoutStatusDropdown">
                <li *ngFor="let layout of groundLayoutStatusTypes">
                  <a class="dropdown-item text-start" (click)="
                      selectStep2Value('groundLayoutStatus', layout.value)
                    ">{{ getTranslatedOption(layout.key) }}</a>
                </li>
              </ul>
            </div>
          </div>

          <!-- Unit Design Field - Only show for specific unit types -->
          <div class="mb-10" *ngIf="shouldShowField('unitDesign')">
            <label class="form-label fw-bold text-start d-block">{{ getTranslatedText('UNIT_DESIGN') }}</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="unitDesignDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  getTranslatedOption(formatUnitTypeKey(step2Form.get("unitDesign")?.value)) ||
                  getTranslatedText('SELECT_UNIT_DESIGN')
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="unitDesignDropdown">
                <li *ngFor="let design of unitDesignTypes">
                  <a class="dropdown-item text-start" (click)="selectStep2Value('unitDesign', design.value)">{{
                    getTranslatedOption(design.key) }}</a>
                </li>
              </ul>
            </div>
          </div>

          <!-- Activity Field - Only show for specific unit types -->
          <div class="mb-10" *ngIf="shouldShowField('activity')">
            <label class="form-label fw-bold text-start d-block">{{ getTranslatedText('ACTIVITY') }}</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="activityDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  getTranslatedOption(formatUnitTypeKey(step2Form.get("activity")?.value)) ||
                  getTranslatedText('SELECT_ACTIVITY')
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="activityDropdown">
                <li *ngFor="let activity of activityTypes">
                  <a class="dropdown-item text-start" (click)="selectStep2Value('activity', activity.value)">{{
                    getTranslatedOption(activity.key) }}</a>
                </li>
              </ul>
            </div>
          </div>

          <div class="mb-10" *ngIf="shouldShowField('deliveryStatus')">
            <label class="form-label fw-bold text-start d-block">{{ getTranslatedText('DELIVERY_STATUS') }}</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="deliveryStatusDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  getTranslatedOption(formatUnitTypeKey(step2Form.get("deliveryStatus")?.value)) ||
                  getTranslatedText('SELECT_DELIVERY_STATUS')
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="deliveryStatusDropdown">
                <li *ngFor="let delivery of deliveryTypes">
                  <a class="dropdown-item text-start" (click)="selectStep2Value('deliveryStatus', delivery.value)">{{
                    getTranslatedOption(delivery.key) }}</a>
                </li>
              </ul>
            </div>
          </div>

          <div class="mb-10" *ngIf="shouldShowField('deliveryDate')">
            <label class="form-label fw-bold text-start d-block">{{ getTranslatedText('DELIVERY_DATE') }}</label>
            <input type="date" class="form-control text-start" formControlName="deliveryDate"
              [placeholder]="getTranslatedText('SELECT_DELIVERY_DATE')" />
          </div>

          <!-- Legal Status Field - Only show for specific unit types -->
          <div class="mb-10" *ngIf="shouldShowField('legalStatus')">
            <label class="form-label fw-bold text-start d-block">
              {{ getTranslatedText('LEGAL_STATUS') }}
            </label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="legalStatusDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  getTranslatedOption(formatUnitTypeKey(step2Form.get("legalStatus")?.value)) ||
                  getTranslatedText('SELECT_LEGAL_STATUS')
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="legalStatusDropdown">
                <li *ngFor="let legal of legalTypes">
                  <a class="dropdown-item text-start" (click)="selectStep2Value('legalStatus', legal.value)">{{
                    getTranslatedOption(legal.key) }}</a>
                </li>
              </ul>
            </div>
          </div>

          <!-- Financial Status Field -->
          <div class="mb-10" *ngIf="shouldShowField('financialStatus')">
            <label class="form-label fw-bold text-start d-block">
              {{ getTranslatedText('FINANCIAL_STATUS') }}
            </label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="financialStatusDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  getTranslatedOption(formatUnitTypeKey(step2Form.get("financialStatus")?.value)) ||
                  getTranslatedText('SELECT_FINANCIAL_STATUS')
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="financialStatusDropdown">
                <li *ngFor="let financial of financialStatusTypes">
                  <a class="dropdown-item text-start" (click)="
                      selectStep2Value('financialStatus', financial.value)
                    ">{{ getTranslatedOption(financial.key) }}</a>
                </li>
              </ul>
            </div>
          </div>

          <!-- Parking Status Field -->
          <div class="mb-10" *ngIf="shouldShowField('parkingStatus')">
            <label class="form-label fw-bold text-start d-block">
              Parking Status
            </label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="parkingStatusDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  formatUnitTypeKey(step2Form.get("parkingStatus")?.value) ||
                  "Select parking status"
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="parkingStatusDropdown">
                <li *ngFor="let parking of financialStatusTypes">
                  <a class="dropdown-item text-start" (click)="
                      selectStep2Value('parkingStatus', parking.value)
                    ">{{ parking.key }}</a>
                </li>
              </ul>
            </div>
          </div>

          <!-- Club Status Field -->
          <div class="mb-10" *ngIf="shouldShowField('clubStatus')">
            <label class="form-label fw-bold text-start d-block">
              Club Status
            </label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="clubStatusDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  formatUnitTypeKey(step2Form.get("clubStatus")?.value) ||
                  "Select financial status"
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="clubStatusDropdown">
                <li *ngFor="let club of financialStatusTypes">
                  <a class="dropdown-item text-start" (click)="
                      selectStep2Value('clubStatus', club.value)
                    ">{{ club.key }}</a>
                </li>
              </ul>
            </div>
          </div>

          <!-- Parking Status Field
          <div class="mb-10" *ngIf="shouldShowField('parkingStatus')">
            <label class="form-label fw-bold text-start d-block">
              Parking Status
            </label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="parkingStatusDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  formatUnitTypeKey(step2Form.get("parkingStatus")?.value) ||
                  "Select parking status"
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="parkingStatusDropdown">
                <li *ngFor="let parking of financialStatusTypes">
                  <a class="dropdown-item text-start" (click)="
                      selectStep2Value('parkingStatus', parking.value)
                    ">{{ parking.key }}</a>
                </li>
              </ul>
            </div>
          </div>

           Club Status Field --
          <div class="mb-10" *ngIf="shouldShowField('clubStatus')">
            <label class="form-label fw-bold text-start d-block">
              Club Status
            </label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="clubStatusDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  formatUnitTypeKey(step2Form.get("clubStatus")?.value) ||
                  "Select financial status"
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="clubStatusDropdown">
                <li *ngFor="let club of financialStatusTypes">
                  <a class="dropdown-item text-start" (click)="
                      selectStep2Value('clubStatus', club.value)
                    ">{{ club.key }}</a>
                </li>
              </ul>
            </div>
          </div> -->

          <div class="mb-10" *ngIf="shouldShowField('otherAccessories')">
            <label class="form-label fw-bold text-start d-block">
              {{ getTranslatedText('OTHER_ACCESSORIES') }}</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="additionalAmenitiesDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  getSelectedAccessoriesText() || getTranslatedText('SELECT_ADDITIONAL_AMENITIES')
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100 p-3" aria-labelledby="additionalAmenitiesDropdown">
                <!-- All The Above Are Suitable - Special handling -->
                <li class="mb-2">
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="accessory_alltheabovearesuitable" [checked]="
                        isAccessorySelected('all_the_above_are_suitable')
                      " (change)="onAllAccessoriesChange($event)" />
                    <label class="form-check-label text-start" for="accessory_alltheabovearesuitable">
                      {{ getTranslatedOption('All The Above Are Suitable') }}
                    </label>
                  </div>
                </li>

                <hr class="my-2" />

                <!-- Individual accessories -->
                <li *ngFor="let accessory of otherAccessoriesTypes" class="mb-2">
                  <div class="form-check" *ngIf="accessory.value !== 'alltheabovearesuitable'">
                    <input class="form-check-input" type="checkbox" [id]="'accessory_' + accessory.value"
                      [checked]="isAccessorySelected(accessory.value)" (change)="toggleAccessory(accessory.value)" />
                    <label class="form-check-label text-start" [for]="'accessory_' + accessory.value">
                      {{ getTranslatedOption(accessory.key) }}
                    </label>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Step 3: Project type   -->
        <div *ngIf="currentStep === 3" [formGroup]="step3Form" class="px-2 px-md-0">
          <!-- Requested Over -->
          <div class="mb-10" *ngIf="shouldShowField('requestedOver')">
            <label class="form-label fw-bold text-start d-block">
              {{ getTranslatedText('REQUESTED_OVER') }}
            </label>
            <input type="text" class="form-control text-start" formControlName="requestedOver"
              [placeholder]="getTranslatedText('ENTER_REQUESTED_OVER')" [ngClass]="{
                'is-invalid':
                  step3Form.get('requestedOver')?.invalid &&
                  (step3Form.get('requestedOver')?.touched ||
                    step3Form.get('requestedOver')?.dirty)
              }" />
            <div *ngIf="
                step3Form.get('requestedOver')?.invalid &&
                (step3Form.get('requestedOver')?.touched ||
                  step3Form.get('requestedOver')?.dirty)
              " class="invalid-feedback">
              <div *ngIf="step3Form.get('requestedOver')?.errors?.['maxlength']">
                {{ getTranslatedText('REQUESTED_OVER_MAX_LENGTH') }}
              </div>
            </div>
          </div>

          <!-- Rent Recurrence -->
          <div class="mb-10" *ngIf="shouldShowField('rentRecurrence')">
            <label class="form-label fw-bold text-start d-block">
              {{ getTranslatedText('RENT_RECURRENCE') }}
            </label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="rentRecurrenceDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  getTranslatedOption(formatUnitTypeKey(step3Form.get("rentRecurrence")?.value)) ||
                  getTranslatedText('SELECT_RENT_RECURRENCE')
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="rentRecurrenceDropdown">
                <li *ngFor="let recurrence of rentRecurrenceTypes">
                  <a class="dropdown-item text-start" (click)="
                      selectStep3Value('rentRecurrence', recurrence.value)
                    ">{{ getTranslatedOption(recurrence.key) }}</a>
                </li>
              </ul>
            </div>
          </div>

          <!-- Daily Rent -->
          <div class="mb-10" *ngIf="
              shouldShowField('dailyRent') &&
              step3Form.get('rentRecurrence')?.value === 'daily'
            ">
            <label class="form-label fw-bold text-start d-block">
              {{ getTranslatedText('DAILY_RENT') }}
            </label>
            <input type="number" class="form-control text-start" formControlName="dailyRent"
              [placeholder]="getTranslatedText('ENTER_DAILY_RENT')" [ngClass]="{
                'is-invalid':
                  step3Form.get('dailyRent')?.invalid &&
                  (step3Form.get('dailyRent')?.touched ||
                    step3Form.get('dailyRent')?.dirty)
              }" />
            <div *ngIf="
                step3Form.get('dailyRent')?.invalid &&
                (step3Form.get('dailyRent')?.touched ||
                  step3Form.get('dailyRent')?.dirty)
              " class="invalid-feedback">
              <div *ngIf="step3Form.get('dailyRent')?.errors?.['min']">
                {{ getTranslatedText('DAILY_RENT_MIN_ERROR') }}
              </div>
            </div>
          </div>

          <!-- Monthly Rent -->
          <div class="mb-10" *ngIf="
              shouldShowField('monthlyRent') &&
              (step3Form.get('rentRecurrence')?.value === 'monthly' ||
                step3Form.get('rentRecurrence')?.value === 'annually')
            ">
            <label class="form-label fw-bold text-start d-block">
              {{ getTranslatedText('MONTHLY_RENT') }}
            </label>
            <input type="number" class="form-control text-start" formControlName="monthlyRent"
              [placeholder]="getTranslatedText('ENTER_MONTHLY_RENT')" [ngClass]="{
                'is-invalid':
                  step3Form.get('monthlyRent')?.invalid &&
                  (step3Form.get('monthlyRent')?.touched ||
                    step3Form.get('monthlyRent')?.dirty)
              }" />
            <div *ngIf="
                step3Form.get('monthlyRent')?.invalid &&
                (step3Form.get('monthlyRent')?.touched ||
                  step3Form.get('monthlyRent')?.dirty)
              " class="invalid-feedback">
              <div *ngIf="step3Form.get('monthlyRent')?.errors?.['min']">
                {{ getTranslatedText('MONTHLY_RENT_MIN_ERROR') }}
              </div>
            </div>
          </div>

          <!-- Payment System -->
          <div class="mb-10" *ngIf="shouldShowField('paymentSystem')">
            <label class="form-label fw-bold text-start d-block">
              {{ getTranslatedText('PAYMENT_SYSTEM') }}
            </label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="paymentSystemDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  getTranslatedOption(formatUnitTypeKey(step3Form.get("paymentSystem")?.value)) ||
                  getTranslatedText('SELECT_PAYMENT_SYSTEM')
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="paymentSystemDropdown">
                <li *ngFor="let payment of paymentTypes">
                  <a class="dropdown-item text-start" (click)="selectStep3Value('paymentSystem', payment.value)">{{
                    getTranslatedOption(payment.key) }}</a>
                </li>
              </ul>
            </div>
          </div>

          <!-- Cash Price Fields - Show when Cash is selected -->
          <ng-container *ngIf="
              shouldShowCashFields() && shouldShowField('pricePerMeterInCash')
            ">
            <div class="mb-10">
              <label class="form-label fw-bold text-start d-block">{{ getTranslatedText('PRICE_PER_METER_CASH')
                }}</label>
              <input type="number" class="form-control text-start" formControlName="pricePerMeterInCash"
                [placeholder]="getTranslatedText('ENTER_PRICE_PER_METER_CASH')" min="0" />
            </div>

            <div class="mb-10">
              <label class="form-label fw-bold text-start d-block">{{ getTranslatedText('TOTAL_PRICE_CASH') }}</label>
              <input type="number" class="form-control text-start" formControlName="totalPriceInCash"
                [placeholder]="getTranslatedText('ENTER_TOTAL_PRICE_CASH')" min="0" />
            </div>
          </ng-container>

          <!-- Installment Price Fields - Show when Installment is selected -->
          <ng-container *ngIf="
              shouldShowInstallmentFields() &&
              shouldShowField('pricePerMeterInInstallment')
            ">
            <div class="mb-10">
              <label class="form-label fw-bold text-start d-block">{{ getTranslatedText('PRICE_PER_METER_INSTALLMENT')
                }}</label>
              <input type="number" class="form-control text-start" formControlName="pricePerMeterInInstallment"
                [placeholder]="getTranslatedText('ENTER_PRICE_PER_METER_INSTALLMENT')" min="0" />
            </div>

            <div class="mb-10" *ngIf="shouldShowField('totalPriceInInstallment')">
              <label class="form-label fw-bold text-start d-block">{{ getTranslatedText('TOTAL_PRICE_INSTALLMENT')
                }}</label>
              <input type="number" class="form-control text-start" formControlName="totalPriceInInstallment"
                [placeholder]="getTranslatedText('ENTER_TOTAL_PRICE_INSTALLMENT')" min="0" />
            </div>
          </ng-container>
        </div>

        <!-- Step 4: Project Documents -->
        <div *ngIf="currentStep === 4" [formGroup]="step4Form" class="px-2 px-md-0">
          <!-- Project Documents Cards -->
          <div class="mb-10 upload-card-container">
            <!--    Upload image of main unit -->
            <div class="card mb-5 cursor-pointer">
              <label for="projectUnitImage" class="card-body text-center py-3">
                <div class="upload-icon">
                  <i class="fas fa-arrow-up"></i>
                </div>
                <span class="upload-text">
                  {{ getTranslatedText('UPLOAD_IMAGES') }}
                  <span *ngIf="getFileCount('diagram') > 0" class="badge bg-success ms-2">
                    {{ getFileCount("diagram") }}
                  </span>
                </span>
                <input type="file" id="projectUnitImage" class="d-none" (change)="onFileChange($event, 'diagram')"
                  multiple />
              </label>
            </div>

            <!-- Upload a photos to the gallery -->
            <div class="card mb-5 cursor-pointer">
              <label for="projectLayout" class="card-body text-center py-3">
                <div class="upload-icon">
                  <i class="fas fa-arrow-up"></i>
                </div>
                <span class="upload-text">
                  {{ getTranslatedText('UPLOAD_IMAGES') }}
                  <span *ngIf="getFileCount('layout') > 0" class="badge bg-success ms-2">
                    {{ getFileCount("layout") }}
                  </span>
                </span>
                <input type="file" id="projectLayout" class="d-none" (change)="onFileChange($event, 'layout')"
                  multiple />
              </label>
            </div>
            <!-- Project Videos -->
            <div class="card mb-5 cursor-pointer">
              <label for="videos" class="card-body text-center py-3">
                <div class="upload-icon">
                  <i class="fas fa-arrow-up"></i>
                </div>
                <span class="upload-text">
                  {{ getTranslatedText('UPLOAD_VIDEOS') }}

                  <span *ngIf="getFileCount('videos') > 0" class="badge bg-success ms-2">
                    {{ getFileCount("videos") }}
                  </span>
                </span>
                <input type="file" id="videos" class="d-none" (change)="onFileChange($event, 'videos')" accept="video/*"
                  multiple />
              </label>
            </div>

            <!-- Upload unit plan -->
            <div class="card mb-5 cursor-pointer">
              <label for="locationInMasterPlan" class="card-body text-center py-3">
                <div class="upload-icon">
                  <i class="fas fa-arrow-up"></i>
                </div>
                <span class="upload-text">
                  {{ getTranslatedText('UPLOAD_DOCUMENTS') }}
                  <span *ngIf="getFileCount('locationInMasterPlan') > 0" class="badge bg-success ms-2">
                    {{ getFileCount("locationInMasterPlan") }}
                  </span>
                </span>
                <input type="file" id="locationInMasterPlan" class="d-none"
                  (change)="onFileChange($event, 'locationInMasterPlan')" multiple />
              </label>
            </div>
          </div>
        </div>

        <!-- Step 5: Review & Submit -->
        <div *ngIf="currentStep === 5" [formGroup]="step5Form" class="px-2 px-md-0">
          <div class="text-center py-5">
            <div class="mb-4">
              <i class="fas fa-check-circle text-success" style="font-size: 3rem"></i>
            </div>
            <h4 class="mb-3">{{ getTranslatedText('READY_TO_SUBMIT') }}</h4>
            <p class="text-muted mb-4">
              {{ getTranslatedText('REVIEW_INFORMATION') }}
            </p>
            <div class="alert alert-info text-start">
              <strong>{{ getTranslatedText('NOTE') }}</strong>
              <ul class="list-unstyled mb-0 mt-2">
                <li>{{ getTranslatedText('OWNER_INFO_STEP1') }}</li>
                <li>{{ getTranslatedText('LEGAL_STATUS_STEP2') }}</li>
                <li>{{ getTranslatedText('FORMS_REORGANIZED') }}</li>
              </ul>
            </div>

            <!-- Submit Buttons -->
            <div class="d-flex flex-column gap-3 mt-10">
              <!-- Add Property (Draft) Button -->
              <button type="button" class="btn btn-success px-1 py-2 rounded-pill"
                [disabled]="!isCurrentFormValid() || isSubmittingDraft || isSubmittingPublish"
                (click)="submitForm(false)">
                <i class="fas fa-save me-2"></i> {{ getTranslatedText('ADD_PROPERTY_DRAFT') }}
                <span *ngIf="isSubmittingDraft" class="spinner-border spinner-border-sm align-middle ms-2"></span>
              </button>

              <!-- Add Property & Publish Button -->
              <button type="button" class="btn btn-primary px-1 py-2 rounded-pill"
                [disabled]="!step5Form.valid || isSubmittingDraft || isSubmittingPublish" (click)="submitForm(true)">
                <i class="fas fa-bullhorn me-2"></i> {{ getTranslatedText('ADD_PROPERTY_PUBLISH') }}
                <span *ngIf="isSubmittingPublish" class="spinner-border spinner-border-sm align-middle ms-2"></span>
              </button>
            </div>
          </div>
        </div>

        <!-- Navigation Buttons - Responsive & Space Optimized -->
        <div class="mt-4 mt-md-6">
          <!-- Step 0 Navigation -->
          <div *ngIf="currentStep === 0" class="d-flex flex-column flex-sm-row justify-content-between gap-3">
            <button type="button"
              class="btn btn-light-dark btn-md btn-lg-lg px-4 px-md-6 py-2 py-md-3 order-1 order-sm-2"
              (click)="cancel()">
              {{ getTranslatedText('CANCEL') }}
            </button>

            <button type="button"
              class="btn btn-navy btn-md btn-lg-lg px-6 px-md-10 py-2 py-md-3 rounded-pill order-2 order-sm-1"
              [disabled]="!isCurrentFormValid()" (click)="nextStep()">
              <span class="indicator-label text-white">
                <span class="d-none d-sm-inline">{{ getTranslatedText('NEXT_LOCATION_INFORMATION') }}</span>
                <span class="d-sm-none">{{ getTranslatedText('NEXT') }}</span>
              </span>
            </button>
          </div>

          <!-- Step 1 Navigation -->
          <div *ngIf="currentStep === 1" class="d-flex justify-content-center">
            <button type="button"
              class="btn btn-navy btn-md btn-lg-lg px-6 px-md-10 py-2 py-md-3 rounded-pill w-100 w-sm-auto"
              [disabled]="!isCurrentFormValid()" (click)="nextStep()">
              <span class="indicator-label text-white">
                <span class="d-none d-sm-inline">{{ getTranslatedText('NEXT_UNIT_INFORMATION') }}</span>
                <span class="d-sm-none">{{ getTranslatedText('NEXT') }}</span>
              </span>
            </button>
          </div>

          <!-- Steps 2, 3, 4 Navigation -->
          <div *ngIf="currentStep > 1 && currentStep !== totalSteps" class="d-flex justify-content-center">
            <button type="button"
              class="btn btn-navy btn-md btn-lg-lg px-6 px-md-10 py-2 py-md-3 rounded-pill w-100 w-sm-auto"
              [disabled]="!isCurrentFormValid()" (click)="nextStep()">
              <span class="indicator-label text-white">
                <ng-container *ngIf="currentStep === 2">
                  <span class="d-none d-sm-inline">{{ getTranslatedText('NEXT_PAYMENT_DETAILS') }}</span>
                  <span class="d-sm-none">{{ getTranslatedText('NEXT') }}</span>
                </ng-container>
                <ng-container *ngIf="currentStep === 3">
                  <span class="d-none d-sm-inline">{{ getTranslatedText('NEXT_MEDIA_DOCUMENTS') }}</span>
                  <span class="d-sm-none">{{ getTranslatedText('NEXT') }}</span>
                </ng-container>
                <ng-container *ngIf="currentStep === 4">
                  <span class="d-none d-sm-inline">{{ getTranslatedText('NEXT_REVIEW_SUBMIT') }}</span>
                  <span class="d-sm-none">{{ getTranslatedText('NEXT') }}</span>
                </ng-container>
              </span>
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>

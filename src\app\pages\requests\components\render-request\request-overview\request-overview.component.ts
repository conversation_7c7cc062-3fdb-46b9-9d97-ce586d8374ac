import { ChangeDetectorRef, Component, OnInit, OnDestroy } from '@angular/core';
import { RequestService } from '../../../services/request.service';
import { ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import Swal from 'sweetalert2';
import { TranslationService } from 'src/app/modules/i18n';

@Component({
  selector: 'app-request-overview',
  templateUrl: './request-overview.component.html',
  styleUrls: ['./request-overview.component.scss'],
})
export class RequestOverviewComponent implements OnInit, OnDestroy {
  request: any = null;
  requestId: string | null = null;
  private routeSub: Subscription | null = null;
  private requestSub: Subscription | null = null;

  // Modal properties
  modalType: 'image' | 'video' = 'image';
  modalContent: string = '';
  modalTitle: string = '';

  constructor(
    protected cd: ChangeDetectorRef,
    protected requestService: RequestService,
    private route: ActivatedRoute,
    public translationService: TranslationService
  ) {}

  ngOnInit() {
     if (this.route.parent) {
      this.routeSub = this.route.parent.paramMap.subscribe((params) => {
        this.requestId = params.get('id') || this.requestService.getRequestId();
        console.log('RequestOverviewComponent - Request ID:', this.requestId);

        if (this.requestId) {
           this.requestSub = this.requestService.getRequest().subscribe((request) => {
            this.request = request;
            console.log('RequestOverviewComponent - Request Data from Service:', this.request);
            this.cd.detectChanges();

             if (!this.request) {
              this.fetchRequest();
            }
          });
        } else {
          console.error('RequestOverviewComponent - No request ID found');
          Swal.fire('Invalid request ID.', '', 'error');
        }
      });
    } else {
      // Handle case where parent route is not available
      this.routeSub = null;
      this.requestId = this.requestService.getRequestId();
      console.error('RequestOverviewComponent - Parent route not found, fallback requestId:', this.requestId);
      if (this.requestId) {
        this.requestSub = this.requestService.getRequest().subscribe((request) => {
          this.request = request;
          console.log('RequestOverviewComponent - Request Data from Service:', this.request);
          this.cd.detectChanges();
          if (!this.request) {
            this.fetchRequest();
          }
        });
      } else {
        console.error('RequestOverviewComponent - No request ID available');
        Swal.fire('Invalid request ID.', '', 'error');
      }
    }
  }

  ngOnDestroy() {
    if (this.routeSub) {
      this.routeSub.unsubscribe();
    }
    if (this.requestSub) {
      this.requestSub.unsubscribe();
    }
  }

  fetchRequest() {
    if (this.requestId) {
      this.requestService.getRequestById(this.requestId).subscribe({
        next: (response: any) => {
          this.request = response.data;
          this.requestService.setRequest(this.request);
          console.log('RequestOverviewComponent - Fetched Request Data:', this.request);
          this.cd.detectChanges();
        },
        error: (error: any) => {
          console.error('RequestOverviewComponent - Error fetching request:', error);
          this.cd.detectChanges();
          Swal.fire('Failed to load data. Please try again later.', '', 'error');
        },
      });
    }
  }

  // Modal methods
  openImageModal(imageUrl: string, title: string) {
    this.modalType = 'image';
    this.modalContent = imageUrl;
    this.modalTitle = title;
  }

  openVideoModal(videoUrl: string, title: string = 'Video') {
    this.modalType = 'video';
    this.modalContent = videoUrl;
    this.modalTitle = title;
  }

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        // Section Headers
        'BASIC_INFORMATION': 'المعلومات الأساسية',
        'LOCATION_DETAILS': 'تفاصيل الموقع',
        'ADDRESS_INFORMATION': 'معلومات العنوان',
        'PROPERTY_DETAILS': 'تفاصيل العقار',
        'STATUS_INFORMATION': 'معلومات الحالة',
        'ADDITIONAL_INFORMATION': 'معلومات إضافية',
        'FINANCIAL_NOTES': 'المالية والملاحظات',
        'MEDIA_GALLERY': 'معرض الوسائط',
        'REQUEST_INFORMATION': 'معلومات الطلب',

        // Basic Information
        'OPERATION_TYPE': 'نوع العملية',
        'SPECIALIZATION': 'التخصص',
        'UNIT_TYPE': 'نوع الوحدة',

        // Location
        'CITY': 'المدينة',
        'AREA': 'المنطقة',
        'SUBAREA': 'المنطقة الفرعية',
        'DETAILED_ADDRESS': 'العنوان التفصيلي',
        'ADDRESS_LINK': 'رابط العنوان',
        'VIEW_ON_MAP': 'عرض على الخريطة',

        // Property Details
        'MALL_NAME': 'اسم المول',
        'BUILDING_NUMBER': 'رقم المبنى',
        'UNIT_NUMBER': 'رقم الوحدة',
        'FLOOR': 'الطابق',
        'FLOOR_NUMBER': 'رقم الطابق',
        'NUMBER_OF_FLOORS': 'عدد الطوابق',
        'UNIT_AREA': 'مساحة الوحدة',
        'GROUND_AREA': 'مساحة الأرض',
        'BUILDING_AREA': 'مساحة المبنى',
        'NUMBER_OF_ROOMS': 'عدد الغرف',
        'NUMBER_OF_BATHROOMS': 'عدد الحمامات',
        'GARDEN_AREA': 'مساحة الحديقة',
        'TERRACE_AREA': 'مساحة التراس',
        'UNIT_FACING': 'اتجاه الوحدة',
        'UNIT_VIEW': 'إطلالة الوحدة',
        'UNIT_LAYOUT_STATUS': 'حالة تخطيط الوحدة',
        'BUILDING_LAYOUT_STATUS': 'حالة تخطيط المبنى',
        'UNIT_DESIGN': 'تصميم الوحدة',
        'UNIT_DESCRIPTION': 'وصف الوحدة',

        // Status Information
        'DELIVERY_STATUS': 'حالة التسليم',
        'DELIVERY_DATE': 'تاريخ التسليم',
        'FINISHING_STATUS': 'حالة التشطيب',
        'FIT_OUT_CONDITION': 'حالة التجهيز',
        'LEGAL_STATUS': 'الحالة القانونية',
        'BUILDING_LICENSE': 'رخصة البناء',
        'BUILDING_DEADLINE': 'موعد انتهاء البناء',
        'ACTIVITY': 'النشاط',
        'FINANCIAL_STATUS': 'الحالة المالية',

        // Additional Information
        'OTHER_ACCESSORIES': 'الإكسسوارات الأخرى',

        // Financial & Notes
        'PAYMENT_METHOD': 'طريقة الدفع',
        'UNIT_PRICE': 'سعر الوحدة',
        'UNIT_PRICE_SUGGESTION': 'اقتراح سعر الوحدة',
        'NOTES': 'الملاحظات',
        'APPROVED': 'موافق عليه',
        'PENDING': 'في الانتظار',

        // Media Gallery
        'MAIN_IMAGE': 'الصورة الرئيسية',
        'GALLERY_MEDIA': 'وسائط المعرض',
        'UNIT_PLAN': 'مخطط الوحدة',
        'GALLERY_IMAGE': 'صورة المعرض',
        'GALLERY_VIDEO': 'فيديو المعرض',

        // Request Information
        'CREATED_AT': 'تاريخ الإنشاء',

        // Modal
        'CLOSE': 'إغلاق',

        // Missing translations
        'N_A': 'غير متوفر',
        'LOADING': 'جاري التحميل...'
      },
      'en': {
        // Section Headers
        'BASIC_INFORMATION': 'Basic Information',
        'LOCATION_DETAILS': 'Location Details',
        'ADDRESS_INFORMATION': 'Address Information',
        'PROPERTY_DETAILS': 'Property Details',
        'STATUS_INFORMATION': 'Status Information',
        'ADDITIONAL_INFORMATION': 'Additional Information',
        'FINANCIAL_NOTES': 'Financial & Notes',
        'MEDIA_GALLERY': 'Media Gallery',
        'REQUEST_INFORMATION': 'Request Information',

        // Basic Information
        'OPERATION_TYPE': 'Operation Type',
        'SPECIALIZATION': 'Specialization',
        'UNIT_TYPE': 'Unit Type',

        // Location
        'CITY': 'City',
        'AREA': 'Area',
        'SUBAREA': 'Subarea',
        'DETAILED_ADDRESS': 'Detailed Address',
        'ADDRESS_LINK': 'Address Link',
        'VIEW_ON_MAP': 'View on Map',

        // Property Details
        'MALL_NAME': 'Mall Name',
        'BUILDING_NUMBER': 'Building Number',
        'UNIT_NUMBER': 'Unit Number',
        'FLOOR': 'Floor',
        'FLOOR_NUMBER': 'Floor Number',
        'NUMBER_OF_FLOORS': 'Number of Floors',
        'UNIT_AREA': 'Unit Area',
        'GROUND_AREA': 'Ground Area',
        'BUILDING_AREA': 'Building Area',
        'NUMBER_OF_ROOMS': 'Number of Rooms',
        'NUMBER_OF_BATHROOMS': 'Number of Bathrooms',
        'GARDEN_AREA': 'Garden Area',
        'TERRACE_AREA': 'Terrace Area',
        'UNIT_FACING': 'Unit Facing',
        'UNIT_VIEW': 'Unit View',
        'UNIT_LAYOUT_STATUS': 'Unit Layout Status',
        'BUILDING_LAYOUT_STATUS': 'Building Layout Status',
        'UNIT_DESIGN': 'Unit Design',
        'UNIT_DESCRIPTION': 'Unit Description',

        // Status Information
        'DELIVERY_STATUS': 'Delivery Status',
        'DELIVERY_DATE': 'Delivery Date',
        'FINISHING_STATUS': 'Finishing Status',
        'FIT_OUT_CONDITION': 'Fit Out Condition',
        'LEGAL_STATUS': 'Legal Status',
        'BUILDING_LICENSE': 'Building License',
        'BUILDING_DEADLINE': 'Building Deadline',
        'ACTIVITY': 'Activity',
        'FINANCIAL_STATUS': 'Financial Status',

        // Additional Information
        'OTHER_ACCESSORIES': 'Other Accessories',

        // Financial & Notes
        'PAYMENT_METHOD': 'Payment Method',
        'UNIT_PRICE': 'Unit Price',
        'UNIT_PRICE_SUGGESTION': 'Unit Price Suggestion',
        'NOTES': 'Notes',
        'APPROVED': 'APPROVED',
        'PENDING': 'PENDING',

        // Media Gallery
        'MAIN_IMAGE': 'Main Image',
        'GALLERY_MEDIA': 'Gallery Media',
        'UNIT_PLAN': 'Unit Plan',
        'GALLERY_IMAGE': 'Gallery Image',
        'GALLERY_VIDEO': 'Gallery Video',

        // Request Information
        'CREATED_AT': 'Created At',

        // Modal
        'CLOSE': 'Close',

        // Missing translations
        'N_A': 'N/A',
        'LOADING': 'Loading...'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }
}
